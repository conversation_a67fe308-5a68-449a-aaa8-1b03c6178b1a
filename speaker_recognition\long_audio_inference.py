#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长语音声纹识别实时推理脚本
使用WebRTC VAD进行人声检测，对含人声的音频进行声纹识别
避免使用临时文件，提高实时性
"""

import os
import sys
import numpy as np
import librosa
import webrtcvad
import time
from pathlib import Path

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# TensorFlow导入
import tensorflow as tf
from tensorflow import keras

# 导入特征提取器
from feature_extractor import SpeakerFeatureExtractor, ALL_SPEAKERS, NMSISMFCCExtractor

# 配置参数
SAMPLE_RATE = 8000
SEGMENT_DURATION = 2.0  # 每个推理段的长度（秒）
SEGMENT_SAMPLES = int(SAMPLE_RATE * SEGMENT_DURATION)  # 16000样本
UPDATE_INTERVAL = 2  # 更新间隔（秒）
UPDATE_SAMPLES = int(SAMPLE_RATE * UPDATE_INTERVAL)  # 12000样本

# VAD配置
VAD_FRAME_DURATION = 30  # VAD帧长度（毫秒）：10, 20, 30
VAD_FRAME_SAMPLES = int(SAMPLE_RATE * VAD_FRAME_DURATION / 1000)  # 240样本
VAD_AGGRESSIVENESS = 3  # VAD敏感度：0-3，数值越高越敏感

# 路径配置
CURRENT_DIR = Path(__file__).parent
MODEL_PATH = CURRENT_DIR / 'model' / 'speaker_model.h5'

class LongAudioInference:
    """长语音声纹识别推理器"""
    
    def __init__(self):
        """初始化推理器"""
        print("🎤 长语音声纹识别实时推理系统")
        print("=" * 60)
        
        # 初始化VAD
        self.vad = webrtcvad.Vad(VAD_AGGRESSIVENESS)
        print(f"🔊 WebRTC VAD初始化完成 (敏感度: {VAD_AGGRESSIVENESS})")
        
        # 初始化特征提取器（直接使用NMSIS提取器，避免临时文件）
        self.mfcc_extractor = NMSISMFCCExtractor()
        print("🔧 MFCC特征提取器初始化完成")
        
        # 加载模型
        self.model = self._load_model()
        print("🤖 声纹识别模型加载完成")
        
        # 初始化缓冲区
        self.audio_buffer = np.array([], dtype=np.float32)
        self.current_time = 0.0
        self.debug_mode = False
        self.confidence_threshold = 0.6

        print(f"⚙️ 配置参数:")
        print(f"   更新间隔: {UPDATE_INTERVAL}秒")
        print(f"   推理段长度: {SEGMENT_DURATION}秒")
        print(f"   VAD帧长度: {VAD_FRAME_DURATION}ms")
        print("=" * 60)
    
    def _load_model(self):
        """加载训练好的模型"""
        if not MODEL_PATH.exists():
            raise FileNotFoundError(f"模型文件不存在: {MODEL_PATH}")
        
        model = keras.models.load_model(str(MODEL_PATH))
        return model
    
    def _detect_voice_activity(self, audio_segment):
        """
        使用WebRTC VAD检测语音活动

        Args:
            audio_segment: 音频段，numpy数组

        Returns:
            tuple: (bool, float) - (是否有人声, 人声比例)
        """
        # 首先检查音频能量，如果太低直接返回无人声
        audio_energy = np.mean(audio_segment ** 2)
        if audio_energy < 1e-6:  # 能量阈值
            return False, 0.0

        # 确保音频是16位整数格式（WebRTC VAD要求）
        # 先进行归一化，避免溢出
        audio_normalized = audio_segment / (np.max(np.abs(audio_segment)) + 1e-8)
        audio_int16 = (audio_normalized * 32767).astype(np.int16)

        # 将音频分成VAD帧进行检测
        voice_frames = 0
        total_frames = 0

        for i in range(0, len(audio_int16) - VAD_FRAME_SAMPLES + 1, VAD_FRAME_SAMPLES):
            frame = audio_int16[i:i + VAD_FRAME_SAMPLES]

            # 确保帧长度正确
            if len(frame) == VAD_FRAME_SAMPLES:
                try:
                    # WebRTC VAD检测
                    is_speech = self.vad.is_speech(frame.tobytes(), SAMPLE_RATE)
                    if is_speech:
                        voice_frames += 1
                    total_frames += 1
                except Exception as e:
                    # VAD检测失败，跳过该帧
                    continue

        # 计算人声比例
        if total_frames > 0:
            voice_ratio = voice_frames / total_frames
            # 如果超过40%的帧检测到人声，则认为该段有人声
            has_voice = voice_ratio > 0.6
            return has_voice, voice_ratio
        else:
            return False, 0.0
    
    def _extract_features_from_audio(self, audio_segment):
        """
        直接从音频数据提取MFCC特征，避免使用临时文件
        
        Args:
            audio_segment: 音频段，numpy数组，长度为2秒
            
        Returns:
            features: MFCC特征，形状为(1, 198, 12, 1)
        """
        # 确保音频长度为2秒
        if len(audio_segment) != SEGMENT_SAMPLES:
            if len(audio_segment) < SEGMENT_SAMPLES:
                # 填充到2秒
                audio_segment = np.pad(audio_segment, (0, SEGMENT_SAMPLES - len(audio_segment)), 'constant')
            else:
                # 截取前2秒
                audio_segment = audio_segment[:SEGMENT_SAMPLES]
        
        # 使用NMSIS MFCC提取器直接提取特征
        mfcc_tensor = self.mfcc_extractor(audio_segment)  # (1, 13, 198)
        
        # 转换为numpy格式：时间×特征
        mfcc_feat = mfcc_tensor.squeeze(0).numpy().T  # (198, 13)
        
        # 去除C0系数（与训练时保持一致）
        mfcc_feat = mfcc_feat[:, 1:]  # (198, 12)
        
        # 添加batch和channel维度
        mfcc_feat = mfcc_feat.reshape(1, 198, 12, 1)
        
        return mfcc_feat.astype(np.float32)
    
    def _predict_speaker(self, features):
        """
        使用模型进行声纹识别预测
        
        Args:
            features: 输入特征
            
        Returns:
            predicted_speaker: 预测的说话人
            confidence: 预测置信度
        """
        # 进行预测
        predictions = self.model.predict(features, verbose=0)
        
        # 获取预测结果
        predicted_class = np.argmax(predictions[0])
        confidence = np.max(predictions[0])
        predicted_speaker = ALL_SPEAKERS[predicted_class]
        
        return predicted_speaker, confidence
    
    def _format_speaker_name(self, speaker_name):
        """
        格式化说话人名称，显示所有具体的说话人ID

        Args:
            speaker_name: 英文说话人名称

        Returns:
            formatted_name: 格式化后的名称
        """
        # 目标说话人的中文映射
        target_mapping = {
            'XiaoXin': '小芯',
            'XiaoYuan': '小源',
            'XiaoSi': '小思',
            'XiaoLai': '小来'
        }

        # 如果是目标说话人，返回中文名
        if speaker_name in target_mapping:
            return target_mapping[speaker_name]

        # 如果是others，返回"其他说话人"
        if speaker_name == 'others':
            return '其他说话人'

        # 其他情况（如ID1-ID11），直接返回原始ID
        return speaker_name
    
    def process_long_audio(self, audio_path):
        """
        处理长语音文件，进行实时声纹识别
        
        Args:
            audio_path: 长语音文件路径
        """
        print(f"🎵 开始处理长语音文件: {audio_path}")
        
        # 加载音频文件
        try:
            audio, sr = librosa.load(audio_path, sr=SAMPLE_RATE, mono=True)
            print(f"   音频长度: {len(audio)/sr:.2f}秒")
            print(f"   采样率: {sr}Hz")
        except Exception as e:
            print(f"❌ 音频文件加载失败: {e}")
            return
        
        print("\n🚀 开始实时推理...")
        print("=" * 60)
        
        # 处理音频
        processed_samples = 0
        
        while processed_samples + UPDATE_SAMPLES <= len(audio):
            # 更新当前时间
            self.current_time = processed_samples / SAMPLE_RATE + UPDATE_INTERVAL
            
            # 获取当前处理段（用于推理的2秒音频）
            if processed_samples + SEGMENT_SAMPLES <= len(audio):
                current_segment = audio[processed_samples:processed_samples + SEGMENT_SAMPLES]
            else:
                # 最后一段，填充到2秒
                remaining_audio = audio[processed_samples:]
                current_segment = np.pad(remaining_audio, (0, SEGMENT_SAMPLES - len(remaining_audio)), 'constant')
            
            # VAD检测
            has_voice, voice_ratio = self._detect_voice_activity(current_segment)

            if has_voice:
                # 检测到人声，进行声纹识别
                try:
                    features = self._extract_features_from_audio(current_segment)
                    speaker, confidence = self._predict_speaker(features)
                    formatted_speaker = self._format_speaker_name(speaker)

                    # 输出结果（显示所有具体的说话人ID）
                    time_ms = int(self.current_time * 1000)
                    result_text = f"{time_ms} ms，说话人 ID：{formatted_speaker}"
                    if self.debug_mode:
                        result_text += f" (置信度: {confidence:.3f}, 人声比例: {voice_ratio:.3f})"
                    print(result_text)

                except Exception as e:
                    print(f"{int(self.current_time * 1000)} ms，推理失败：{e}")
            else:
                # 无人声
                time_ms = int(self.current_time * 1000)
                result_text = f"{time_ms} ms，无人声"
                if self.debug_mode:
                    result_text += f" (人声比例: {voice_ratio:.3f})"
                print(result_text)
            
            # 移动到下一个更新点
            processed_samples += UPDATE_SAMPLES
        
        print("=" * 60)
        print("✅ 长语音处理完成")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='长语音声纹识别实时推理')
    parser.add_argument('--audio', type=str,
                       default='speaker_recognition/test/003.wav',
                       help='音频文件路径')
    parser.add_argument('--update_interval', type=float, default=2,
                       help='更新间隔（秒）')
    parser.add_argument('--vad_sensitivity', type=int, default=2, choices=[0,1,2,3],
                       help='VAD敏感度 (0-3)')
    parser.add_argument('--confidence_threshold', type=float, default=0.6,
                       help='置信度阈值')
    parser.add_argument('--debug', action='store_true',
                       help='显示调试信息')

    args = parser.parse_args()

    # 更新全局参数
    global UPDATE_INTERVAL, UPDATE_SAMPLES, VAD_AGGRESSIVENESS
    UPDATE_INTERVAL = args.update_interval
    UPDATE_SAMPLES = int(SAMPLE_RATE * UPDATE_INTERVAL)
    VAD_AGGRESSIVENESS = args.vad_sensitivity

    # 检查音频文件
    audio_file = Path(args.audio)
    if not audio_file.exists():
        print(f"❌ 音频文件不存在: {audio_file}")
        return

    try:
        # 创建推理器
        inference = LongAudioInference()

        # 设置调试模式
        inference.debug_mode = args.debug
        inference.confidence_threshold = args.confidence_threshold

        # 处理长语音
        inference.process_long_audio(str(audio_file))

    except Exception as e:
        print(f"❌ 推理过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
