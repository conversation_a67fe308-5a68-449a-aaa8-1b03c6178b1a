#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
声纹识别推理脚本
对单个音频文件进行声纹识别推理
"""

import os
import sys
import numpy as np
import librosa
from pathlib import Path

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# TensorFlow导入
import tensorflow as tf
from tensorflow import keras

# 导入特征提取器
from feature_extractor import SpeakerFeatureExtractor, ALL_SPEAKERS

# 配置参数
SAMPLE_RATE = 8000
DURATION = 2.0
N_FRAMES = 198
N_MFCC = 12  # 训练时使用的MFCC维度（去除C0后）
NUM_CLASSES = 16

# 路径配置
CURRENT_DIR = Path(__file__).parent
MODEL_PATH = CURRENT_DIR / 'model' / 'speaker_model.h5'
TEST_DIR = CURRENT_DIR / 'test'

def preprocess_data(x_test):
    """
    数据预处理（NNOM正确方式：保持原始浮点数据）
    """
    print(f"   原始MFCC数据范围: [{x_test.min():.3f}, {x_test.max():.3f}]")
    print(f"   数据形状: {x_test.shape}")
    print(f"   数据类型: {x_test.dtype}")

    # 直接返回原始数据，让NNOM处理量化
    return x_test.astype(np.float32)

def load_audio_file(audio_path):
    """
    加载音频文件并预处理
    
    Args:
        audio_path: 音频文件路径
        
    Returns:
        audio: 预处理后的音频数据
    """
    print(f"🎵 加载音频文件: {audio_path}")
    
    if not os.path.exists(audio_path):
        raise FileNotFoundError(f"音频文件不存在: {audio_path}")
    
    # 使用librosa加载音频
    audio, sr = librosa.load(audio_path, sr=SAMPLE_RATE, mono=True)
    
    print(f"   原始采样率: {sr}Hz")
    print(f"   音频长度: {len(audio)} 样本 ({len(audio)/sr:.2f}秒)")
    
    # 确保音频长度为2秒
    target_samples = int(SAMPLE_RATE * DURATION)
    if len(audio) != target_samples:
        if len(audio) < target_samples:
            # 音频太短，用零填充
            audio = np.pad(audio, (0, target_samples - len(audio)), 'constant')
            print(f"   音频填充到 {DURATION}秒")
        else:
            # 音频太长，截取前2秒
            audio = audio[:target_samples]
            print(f"   音频截取到 {DURATION}秒")
    
    print(f"   处理后长度: {len(audio)} 样本")
    print(f"   音频范围: [{audio.min():.4f}, {audio.max():.4f}]")
    
    return audio

def extract_features(audio):
    """
    从音频中提取MFCC特征
    
    Args:
        audio: 音频数据
        
    Returns:
        features: 提取的MFCC特征，形状为(1, 198, 12, 1)
    """
    print("🔧 提取MFCC特征...")
    
    # 初始化特征提取器
    extractor = SpeakerFeatureExtractor()
    
    # 创建临时音频文件（因为特征提取器需要文件路径）
    temp_audio_path = "temp_audio.wav"
    try:
        # 保存临时音频文件
        try:
            import soundfile as sf
            sf.write(temp_audio_path, audio, SAMPLE_RATE)
        except ImportError:
            # 如果soundfile不可用，使用librosa
            librosa.output.write_wav(temp_audio_path, audio, SAMPLE_RATE)
        
        # 提取完整13维MFCC特征
        mfcc_feat = extractor.extract_mfcc_from_file(temp_audio_path)
        
        if mfcc_feat is None:
            raise ValueError("MFCC特征提取失败")
        
        print(f"   提取的特征形状: {mfcc_feat.shape}")
        print(f"   特征范围: [{mfcc_feat.min():.4f}, {mfcc_feat.max():.4f}]")
        
        # 去除C0系数（与训练时保持一致）
        mfcc_feat = mfcc_feat[:, 1:]  # (198, 12)
        print(f"   去除C0后形状: {mfcc_feat.shape}")
        
        # 添加batch和channel维度
        mfcc_feat = mfcc_feat.reshape(1, N_FRAMES, N_MFCC, 1)
        print(f"   最终特征形状: {mfcc_feat.shape}")
        
        return mfcc_feat
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)

def load_model():
    """
    加载训练好的模型
    
    Returns:
        model: 加载的Keras模型
    """
    print("🤖 加载训练好的模型...")
    
    if not MODEL_PATH.exists():
        raise FileNotFoundError(f"模型文件不存在: {MODEL_PATH}")
    
    # 加载模型
    model = keras.models.load_model(str(MODEL_PATH))
    print(f"✅ 模型加载成功: {MODEL_PATH}")
    
    # 显示模型信息
    print("\n📊 模型信息:")
    print(f"   输入形状: {model.input_shape}")
    print(f"   输出形状: {model.output_shape}")
    print(f"   参数数量: {model.count_params():,}")
    
    return model

def predict_speaker(model, features):
    """
    使用模型进行声纹识别预测
    
    Args:
        model: 训练好的模型
        features: 输入特征
        
    Returns:
        predicted_speaker: 预测的说话人
        confidence: 预测置信度
        all_probs: 所有类别的概率
    """
    print("🔮 进行声纹识别预测...")

    # 预处理特征（NNOM方式：保持原始数据）
    features = preprocess_data(features)
    print(f"   预处理后特征范围: [{features.min():.4f}, {features.max():.4f}]")

    # 进行预测
    predictions = model.predict(features, verbose=0)
    
    # 获取预测结果
    predicted_class = np.argmax(predictions[0])
    confidence = np.max(predictions[0])
    predicted_speaker = ALL_SPEAKERS[predicted_class]
    
    print(f"✅ 预测完成!")
    print(f"   预测说话人: {predicted_speaker}")
    print(f"   预测置信度: {confidence:.4f} ({confidence*100:.2f}%)")
    
    return predicted_speaker, confidence, predictions[0]

def display_detailed_results(all_probs):
    """
    显示详细的预测结果
    
    Args:
        all_probs: 所有类别的概率
    """
    print("\n📊 详细预测结果:")
    print("=" * 50)
    
    # 按概率排序
    sorted_indices = np.argsort(all_probs)[::-1]
    
    print("排名  说话人      概率      置信度")
    print("-" * 50)
    
    for i, idx in enumerate(sorted_indices):
        speaker = ALL_SPEAKERS[idx]
        prob = all_probs[idx]
        
        # 显示前10个结果
        if i < 10:
            rank_symbol = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1:2d}"
            print(f"{rank_symbol}   {speaker:12s} {prob:.4f}    {prob*100:6.2f}%")
    
    # 显示目标说话人的排名
    print("\n🎯 目标说话人排名:")
    target_speakers = ['XiaoXin', 'XiaoYuan', 'XiaoSi', 'XiaoLai']
    for speaker in target_speakers:
        if speaker in ALL_SPEAKERS:
            idx = ALL_SPEAKERS.index(speaker)
            prob = all_probs[idx]
            rank = np.where(sorted_indices == idx)[0][0] + 1
            print(f"   {speaker:12s}: 第{rank:2d}名, 概率 {prob:.4f} ({prob*100:.2f}%)")

def main():
    """
    主函数：执行完整的推理流程
    """
    print("🎤 声纹识别推理系统")
    print("=" * 50)

    # 首先加载模型检查输入形状
    print("🔍 检查模型输入形状...")
    try:
        model = load_model()
        print(f"✅ 模型输入形状: {model.input_shape}")
        print(f"✅ 预期输入形状: (None, {N_FRAMES}, {N_MFCC}, 1)")

        # 验证形状是否匹配
        expected_shape = (None, N_FRAMES, N_MFCC, 1)
        if model.input_shape == expected_shape:
            print("✅ 输入形状匹配正确！")
        else:
            print("❌ 输入形状不匹配！")
            print(f"   模型期望: {model.input_shape}")
            print(f"   推理配置: {expected_shape}")
            return
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return

    # 检查测试音频文件
    test_audio = TEST_DIR / "xiaoxin.wav"

    if not test_audio.exists():
        print(f"❌ 测试音频文件不存在: {test_audio}")
        print("请将音频文件放在 speaker_recognition/test/ 目录下")
        return
    
    try:
        # 1. 加载音频文件
        audio = load_audio_file(str(test_audio))
        
        # 2. 提取特征
        features = extract_features(audio)
        
        # 3. 进行预测
        predicted_speaker, confidence, all_probs = predict_speaker(model, features)
        
        # 4. 显示详细结果
        display_detailed_results(all_probs)

        # 5. 总结
        print("\n🏆 推理总结:")
        print("=" * 50)
        print(f"音频文件: {test_audio.name}")
        print(f"预测结果: {predicted_speaker}")
        print(f"置信度: {confidence:.4f} ({confidence*100:.2f}%)")
        
        # 判断预测质量
        if confidence > 0.9:
            quality = "🟢 非常确信"
        elif confidence > 0.7:
            quality = "🟡 比较确信"
        elif confidence > 0.5:
            quality = "🟠 不太确信"
        else:
            quality = "🔴 不确信"
        
        print(f"预测质量: {quality}")
        
    except Exception as e:
        print(f"❌ 推理过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
