Archive member included to satisfy reference by file (symbol)

D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                              ./galaxy_sdk/bsp/src/portasm.o (xPortTaskSwitch)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
                              ./galaxy_sdk/main.o (soc_init)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o) (soc_rtc_clock_get_freq)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o) (soc_pinmux_set)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
                              ./galaxy_sdk/bsp/src/startup_riscv.o (premain_system_init)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o) (boot_get_zsp_addr)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
                              ./galaxy_sdk/main.o (board_register)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o) (cpu_sleep)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o) (clk_get_rate)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o) (pdm_simulator_device_init)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
                              ./galaxy_sdk/main.o (uart_printf)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o) (vpi_timer_create)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
                              ./galaxy_sdk/main.o (vsd_to_vpi)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o) (vs_logging_level)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
                              ./galaxy_sdk/main.o (osal_create_task)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o) (osal_ms_to_tick)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o) (osal_timer_create)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o) (osal_get_task_name)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o) (osal_malloc)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                              ./galaxy_sdk/bsp/src/portasm.o (pxCurrentTCB)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o) (vListInitialise)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o) (xTimerCreateTimerTask)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o) (pvPortMalloc)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o) (xQueueGenericCreateStatic)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-atexit.o)
                              ./galaxy_sdk/bsp/src/startup_riscv.o (atexit)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-calloc.o)
                              ./galaxy_sdk/modules/external/nnom/src/core/nnom.o (calloc)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-callocr.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-calloc.o) (_calloc_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-malloc.o)
                              ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o (malloc)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-callocr.o) (_malloc_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mlock.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o) (__malloc_lock)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-printf.o)
                              ./galaxy_sdk/modules/external/nnom/src/core/nnom.o (printf)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putchar.o)
                              ./galaxy_sdk/modules/external/nnom/src/core/nnom.o (putchar)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o)
                              ./galaxy_sdk/modules/external/nnom/src/core/nnom.o (puts)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-snprintf.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o) (snprintf)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sprintf.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o) (sprintf)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-vprintf.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o) (vprintf)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wbuf.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o) (__swbuf_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wsetup.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o) (__swsetup_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strnlen.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o) (strnlen)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stpcpy.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o) (stpcpy)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-impure.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-calloc.o) (_impure_ptr)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sbrkr.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o) (_sbrk_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-init.o)
                              ./galaxy_sdk/bsp/src/startup_riscv.o (__libc_init_array)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fini.o)
                              ./galaxy_sdk/bsp/src/startup_riscv.o (__libc_fini_array)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mlock.o) (__retarget_lock_acquire_recursive)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memset.o)
                              ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o (memset)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memcpy.o)
                              ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o (memcpy)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strlen.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o) (strlen)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-atexit.o) (__register_exitproc)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__call_atexit.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o) (__call_exitprocs)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-freer.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-malloc.o) (_free_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-snprintf.o) (_svfprintf_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-printf.o) (_vfprintf_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o) (_printf_i)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wbuf.o) (_fflush_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o) (__sglue)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fvwrite.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o) (__sfvwrite_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fwalk.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o) (_fwalk_sglue)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wsetup.o) (__smakebuf_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putc.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putchar.o) (_putc_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o) (__sread)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memchr.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o) (memchr)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-closer.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o) (_close_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reent.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sbrkr.o) (errno)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fstatr.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o) (_fstat_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-isattyr.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o) (_isatty_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lseekr.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o) (_lseek_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-readr.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o) (_read_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-writer.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o) (_write_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memmove-stub.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o) (memmove)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reallocr.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o) (_realloc_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-msizer.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reallocr.o) (_malloc_usable_size_r)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_divdi3.o)
                              ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o (__divdi3)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_udivdi3.o)
                              ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o (__udivdi3)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_umoddi3.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o) (__umoddi3)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_clz.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_divdi3.o) (__clz_tab)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_close.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-closer.o) (_close)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_close.o) (__get_fdentry)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fstat.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fstatr.o) (_fstat)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_isatty.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-isattyr.o) (_isatty)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_lseek.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lseekr.o) (_lseek)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_open.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o) (_open)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_read.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-readr.o) (_read)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_sbrk.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sbrkr.o) (_sbrk)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_stat_common.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fstat.o) (__stat_common)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_write.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-writer.o) (_write)
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
                              D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o) (e203_uart_init)
D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-errno.o)
                              D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o) (__errno)

Discarded input sections

 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.activation_run
                0x00000000        0xc ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.activation_free
                0x00000000       0x1c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.leaky_relu_run
                0x00000000       0x60 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.adv_relu_run
                0x00000000      0x176 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.tanh_run
                0x00000000       0x6c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.sigmoid_run
                0x00000000       0x6c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.hard_tanh_run
                0x00000000       0x62 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.hard_sigmoid_run
                0x00000000       0x62 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.Activation
                0x00000000       0x72 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.ReLU     0x00000000       0x32 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.LeakyReLU
                0x00000000       0x4e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.AdvReLU  0x00000000       0x64 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.Sigmoid  0x00000000       0x3c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.TanH     0x00000000       0x3c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.act_delete
                0x00000000        0x8 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.act_leaky_relu
                0x00000000       0x40 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.act_adv_relu
                0x00000000       0x56 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.act_tanh
                0x00000000       0x2e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.act_sigmoid
                0x00000000       0x2e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.act_hard_tanh
                0x00000000       0x2e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.act_hard_sigmoid
                0x00000000       0x2e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.act_tensor_run
                0x00000000        0x6 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .srodata.cst4  0x00000000       0x14 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .text.AvgPool  0x00000000       0x34 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .text.default_run
                0x00000000       0x40 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .text.BaseLayer
                0x00000000       0x5e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .text.baselayer_s
                0x00000000       0x1c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .text.concat_build
                0x00000000      0x11e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .text.concat_run
                0x00000000       0xf0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .text.Concat   0x00000000       0x6a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .text.concat_s
                0x00000000       0x20 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .debug_info    0x00000000     0x1033 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .debug_abbrev  0x00000000      0x22b ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .debug_loclists
                0x00000000      0x3f1 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .debug_aranges
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .debug_rnglists
                0x00000000       0x85 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .debug_line    0x00000000      0x848 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .debug_str     0x00000000      0x822 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .debug_line_str
                0x00000000      0x3e4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .debug_frame   0x00000000       0xd4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .text.Conv2D   0x00000000      0x174 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .text.conv_output_length
                0x00000000       0x1e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .text.conv2d_trans_build
                0x00000000      0x144 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .text.conv2d_trans_run
                0x00000000       0xa0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .text.conv2d_trans_s
                0x00000000       0x2c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .text.Conv2DTrans
                0x00000000       0x42 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .text.conv_trans_output_length
                0x00000000       0x10 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .debug_info    0x00000000     0x12cf ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .debug_abbrev  0x00000000      0x25a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .debug_loclists
                0x00000000      0x261 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .debug_aranges
                0x00000000       0x40 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .debug_rnglists
                0x00000000       0x7b ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .debug_line    0x00000000      0x724 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .debug_str     0x00000000      0x9aa ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .debug_line_str
                0x00000000      0x3c1 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .debug_frame   0x00000000       0x9c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .text.cropping_build
                0x00000000       0xa2 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .text.cropping_run
                0x00000000       0x56 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .text.cropping_s
                0x00000000       0x36 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .text.Cropping
                0x00000000       0x2a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .debug_info    0x00000000      0xf65 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .debug_abbrev  0x00000000      0x244 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .debug_loclists
                0x00000000      0x10e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .debug_aranges
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .debug_rnglists
                0x00000000       0x56 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .debug_line    0x00000000      0x428 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .debug_str     0x00000000      0x865 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .debug_line_str
                0x00000000      0x3a5 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .debug_frame   0x00000000       0x90 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .text.Dense    0x00000000      0x126 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .text.dw_conv2d_build
                0x00000000      0x14e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .text.dw_conv2d_run
                0x00000000       0x9e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .text.dw_conv2d_s
                0x00000000       0x2c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .text.DW_Conv2D
                0x00000000       0x42 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .debug_info    0x00000000     0x1240 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .debug_abbrev  0x00000000      0x229 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .debug_loclists
                0x00000000      0x160 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .debug_aranges
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .debug_rnglists
                0x00000000       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .debug_line    0x00000000      0x5c0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .debug_str     0x00000000      0x996 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .debug_line_str
                0x00000000      0x3a4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .debug_frame   0x00000000       0x90 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .text.global_pool_build
                0x00000000      0x134 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .text.GlobalMaxPool
                0x00000000       0x68 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .text.global_maxpool_s
                0x00000000       0x24 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .text.GlobalAvgPool
                0x00000000       0x72 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .text.global_avgpool_s
                0x00000000       0x24 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .text.GlobalSumPool
                0x00000000       0x72 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .text.global_sumpool_s
                0x00000000       0x24 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .debug_info    0x00000000     0x11a5 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .debug_abbrev  0x00000000      0x1e7 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .debug_loclists
                0x00000000      0x121 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .debug_aranges
                0x00000000       0x50 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .debug_rnglists
                0x00000000       0x4c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .debug_line    0x00000000      0x72c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .debug_str     0x00000000      0x8d1 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .debug_line_str
                0x00000000      0x3be ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .debug_frame   0x00000000       0xf4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .text.gru_cell_free
                0x00000000        0x4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .text.gru_cell_build
                0x00000000       0x7a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .text.gru_cell_run
                0x00000000      0x1e6 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .text.gru_cell_s
                0x00000000       0x6c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .debug_info    0x00000000     0x13b8 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .debug_abbrev  0x00000000      0x1d9 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .debug_loclists
                0x00000000      0x259 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .debug_aranges
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .debug_rnglists
                0x00000000       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .debug_line    0x00000000      0x63a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .debug_str     0x00000000      0x9c2 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .debug_line_str
                0x00000000      0x380 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .debug_frame   0x00000000       0xa4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .text.Input    0x00000000       0xc2 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .text.Lambda   0x00000000       0x94 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .text.lambda_s
                0x00000000       0x24 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .debug_info    0x00000000      0xe86 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .debug_abbrev  0x00000000      0x206 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .debug_loclists
                0x00000000      0x1ae ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .debug_aranges
                0x00000000       0x28 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .debug_rnglists
                0x00000000       0x1f ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .debug_line    0x00000000      0x25f ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .debug_str     0x00000000      0x7e9 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .debug_line_str
                0x00000000      0x37b ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .debug_frame   0x00000000       0x64 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .text.lstm_cell_free
                0x00000000        0x4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .text.lstm_cell_q7_q15_build
                0x00000000       0x74 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .text.lstm_cell_q7_q15_run
                0x00000000      0x1dc ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .text.lstm_cell_s
                0x00000000       0x74 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .debug_info    0x00000000     0x1394 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .debug_abbrev  0x00000000      0x207 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .debug_loclists
                0x00000000      0x1fb ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .debug_aranges
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .debug_rnglists
                0x00000000       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .debug_line    0x00000000      0x62d ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .debug_str     0x00000000      0x9d3 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .debug_line_str
                0x00000000      0x3df ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .debug_frame   0x00000000       0xa8 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text.matrix_build
                0x00000000       0x6e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text.add_run  0x00000000       0xa8 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text.sub_run  0x00000000       0xa8 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text.mult_run
                0x00000000       0xa8 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text._same_shape_matrix_layer
                0x00000000       0x52 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text.Add      0x00000000       0x2c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text.Sub      0x00000000       0x2c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text.Mult     0x00000000       0x2e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text.mult_s   0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text.add_s    0x00000000       0x36 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text.sub_s    0x00000000       0x36 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .debug_info    0x00000000     0x1472 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .debug_abbrev  0x00000000      0x2c0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .debug_loclists
                0x00000000      0x55d ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .debug_aranges
                0x00000000       0x70 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .debug_rnglists
                0x00000000       0xa9 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .debug_line    0x00000000      0xb93 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .debug_str     0x00000000      0x8d9 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .debug_line_str
                0x00000000      0x39b ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .debug_frame   0x00000000      0x1e0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .text.Output   0x00000000       0x48 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .text.reshape_run
                0x00000000        0x4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .text.reshape_build
                0x00000000       0x58 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .text.reshape_s
                0x00000000       0x74 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .debug_info    0x00000000      0xeb2 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .debug_abbrev  0x00000000      0x1da ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .debug_loclists
                0x00000000      0x112 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .debug_aranges
                0x00000000       0x30 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .debug_rnglists
                0x00000000       0x28 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .debug_line    0x00000000      0x2d2 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .debug_str     0x00000000      0x7b3 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .debug_line_str
                0x00000000      0x38d ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .debug_frame   0x00000000       0x6c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .text.rnn_free
                0x00000000       0x24 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .text.rnn_build
                0x00000000       0xfc ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .text.rnn_run  0x00000000       0xf4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .text.rnn_s    0x00000000       0x9a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .debug_info    0x00000000     0x11a0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .debug_abbrev  0x00000000      0x231 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .debug_loclists
                0x00000000      0x326 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .debug_aranges
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .debug_rnglists
                0x00000000       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .debug_line    0x00000000      0x874 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .debug_str     0x00000000      0x945 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .debug_line_str
                0x00000000      0x3f3 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .debug_frame   0x00000000       0xf4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .text.simple_cell_free
                0x00000000        0x4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .text.simple_cell_build
                0x00000000       0x72 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .text.simple_cell_run
                0x00000000       0xb6 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .text.simple_cell_s
                0x00000000       0x7c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .debug_info    0x00000000     0x1182 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .debug_abbrev  0x00000000      0x210 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .debug_loclists
                0x00000000      0x1df ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .debug_aranges
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .debug_rnglists
                0x00000000       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .debug_line    0x00000000      0x497 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .debug_str     0x00000000      0x983 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .debug_line_str
                0x00000000      0x3e7 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .debug_frame   0x00000000       0x7c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .text.sumpool_build
                0x00000000       0x2c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .text.sumpool_run
                0x00000000       0x74 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .text.sumpool_s
                0x00000000      0x10e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .text.SumPool  0x00000000       0x34 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .debug_info    0x00000000     0x10ca ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .debug_abbrev  0x00000000      0x277 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .debug_loclists
                0x00000000      0x144 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .debug_aranges
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .debug_rnglists
                0x00000000       0x56 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .debug_line    0x00000000      0x400 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .debug_str     0x00000000      0x883 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .debug_line_str
                0x00000000      0x39c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .debug_frame   0x00000000       0x9c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .text.upsample_build
                0x00000000       0x90 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .text.upsample_run
                0x00000000       0x4c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .text.UpSample
                0x00000000       0x70 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .text.upsample_s
                0x00000000       0x40 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .debug_info    0x00000000      0xfd9 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .debug_abbrev  0x00000000      0x21a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .debug_loclists
                0x00000000      0x148 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .debug_aranges
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .debug_rnglists
                0x00000000       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .debug_line    0x00000000      0x46d ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .debug_str     0x00000000      0x842 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .debug_line_str
                0x00000000      0x3a3 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .debug_frame   0x00000000       0x90 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .text.zero_padding_build
                0x00000000       0xa0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .text.zero_padding_run
                0x00000000       0x56 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .text.ZeroPadding
                0x00000000       0x72 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .text.zeropadding_s
                0x00000000       0x20 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .debug_info    0x00000000      0xfc0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .debug_abbrev  0x00000000      0x218 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .debug_loclists
                0x00000000      0x169 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .debug_aranges
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .debug_rnglists
                0x00000000       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .debug_line    0x00000000      0x455 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .debug_str     0x00000000      0x868 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .debug_line_str
                0x00000000      0x3b3 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .debug_frame   0x00000000       0x9c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.io_list_delete
                0x00000000       0x52 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.nnom_mem_stat
                0x00000000        0xa ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.model_delete
                0x00000000       0xdc ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.nnom_io_length
                0x00000000       0x10 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.nnom_hook_length
                0x00000000       0x10 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.mem_analysis_result
                0x00000000       0x6c ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.block_mem_set
                0x00000000       0x20 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.sequencial_compile
                0x00000000       0x28 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.layer_run
                0x00000000       0x3e ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.model_set_callback
                0x00000000       0x12 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.model_delete_callback
                0x00000000        0x6 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .text.shape_size
                0x00000000       0x1c ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .text.border   0x00000000       0x16 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .text.io_add_aux
                0x00000000       0x32 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .text.tensor_size_byte
                0x00000000       0x34 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .text.tensor_hwc2chw_q7
                0x00000000       0x88 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .text.tensor_chw2hwc_q7
                0x00000000       0x66 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .text.hwc2chw_q7
                0x00000000       0x54 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .text.chw2hwc_q7
                0x00000000       0x6a ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .rodata.prediction_matrix.part.0.str1.4
                0x00000000       0x3c ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.prediction_matrix.part.0
                0x00000000      0x11e ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .rodata.prediction_top_k.part.0.str1.4
                0x00000000       0x39 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.prediction_top_k.part.0
                0x00000000       0xae ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.prediction_create
                0x00000000       0xb6 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.prediction_run
                0x00000000      0x11e ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.prediction_end
                0x00000000        0xc ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.prediction_delete
                0x00000000       0x30 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.prediction_matrix
                0x00000000        0xc ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.prediction_top_k
                0x00000000        0xc ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .rodata.prediction_summary.str1.4
                0x00000000       0xcc ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.prediction_summary
                0x00000000      0x18a ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.nnom_predict
                0x00000000       0xe4 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .rodata.model_stat.str1.4
                0x00000000      0x181 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.model_stat
                0x00000000      0x372 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .rodata.model_io_format.str1.4
                0x00000000       0xc6 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text.model_io_format
                0x00000000      0x18e ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .srodata.cst4  0x00000000        0x8 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .debug_info    0x00000000     0x1e49 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .debug_abbrev  0x00000000      0x47e ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .debug_loclists
                0x00000000      0x9f2 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .debug_aranges
                0x00000000       0x78 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .debug_rnglists
                0x00000000      0x201 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .debug_line    0x00000000     0x125a ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .debug_str     0x00000000      0xb6b ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .debug_line_str
                0x00000000      0x3ea ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .comment       0x00000000       0x23 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .debug_frame   0x00000000      0x340 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.alg_deconv2d_calculate_position
                0x00000000       0xaa ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_avepool_q7_CHW
                0x00000000      0x1aa ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_maxpool_q7_CHW
                0x00000000      0x1a6 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_sumpool_q7_HWC
                0x00000000      0x17c ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_sumpool_q7_CHW
                0x00000000      0x17a ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_up_sampling_q7_HWC
                0x00000000      0x102 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_up_sampling_q7_CHW
                0x00000000      0x12c ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_convolve_CHW_q7_nonsquare
                0x00000000      0x1ea ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_conv_trans_HWC_q7_nonsquare
                0x00000000      0x23c ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_depthwise_separable_conv_HWC_q7_nonsquare
                0x00000000      0x24e ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_depthwise_separable_conv_CHW_q7_nonsquare
                0x00000000      0x280 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_zero_padding_HWC_q7
                0x00000000       0xc8 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_zero_padding_CHW_q7
                0x00000000      0x100 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_cropping_HWC_q7
                0x00000000       0x66 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_cropping_CHW_q7
                0x00000000       0x8e ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_dot_q7_opt
                0x00000000      0x2a2 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_dot_q7
                0x00000000       0x6c ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_fully_connected_q7
                0x00000000       0xe4 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_hard_sigmoid_q7
                0x00000000       0x64 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_hard_tanh_q7
                0x00000000       0x8e ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_sigmoid_q7
                0x00000000       0x58 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_tanh_q7
                0x00000000       0x64 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_leaky_relu_q7
                0x00000000       0x28 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_adv_relu_q7
                0x00000000       0x38 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_mult_q7
                0x00000000       0x50 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_add_q7
                0x00000000       0x4e ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_sub_q7
                0x00000000       0x50 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_multiple_add_q7
                0x00000000       0x5e ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_multiple_mult_q7
                0x00000000       0x5c ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_multiple_sub_q7
                0x00000000       0x66 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_q7_to_q15_no_shift
                0x00000000       0x7a ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_q7_to_q15
                0x00000000       0x8a ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text.local_q15_to_q7
                0x00000000       0x1a ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .rodata.nnom_tanh_table_q7
                0x00000000      0x100 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .rodata.nnom_sigmoid_table_q7
                0x00000000      0x100 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .srodata.cst4  0x00000000        0x8 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .text          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .data          0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .bss           0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.alg_deconv2d_calculate_position
                0x00000000       0xaa ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_activation_q15
                0x00000000       0x72 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_avepool_q15_CHW
                0x00000000      0x1d8 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_maxpool_q15_HWC
                0x00000000      0x184 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_maxpool_q15_CHW
                0x00000000      0x1b0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_sumpool_q15_HWC
                0x00000000      0x1a0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_sumpool_q15_CHW
                0x00000000      0x1a8 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_up_sampling_q15_HWC
                0x00000000      0x116 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_up_sampling_q15_CHW
                0x00000000      0x13c ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_convolve_CHW_q15_nonsquare
                0x00000000      0x228 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_conv_trans_HWC_q15_nonsquare
                0x00000000      0x2ae ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_depthwise_separable_conv_HWC_q15_nonsquare
                0x00000000      0x292 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_depthwise_separable_conv_CHW_q15_nonsquare
                0x00000000      0x2c0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_zero_padding_HWC_q15
                0x00000000       0xdc ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_zero_padding_CHW_q15
                0x00000000      0x110 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_cropping_HWC_q15
                0x00000000       0x70 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_cropping_CHW_q15
                0x00000000       0xa2 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_dot_q15
                0x00000000       0xae ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_dot_q15_opt
                0x00000000      0x31a ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_fully_connected_mat_q7_vec_q15_opt
                0x00000000      0x376 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_fully_connected_mat_q7_vec_q15
                0x00000000      0x150 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_softmax_q15
                0x00000000       0xe6 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_hard_sigmoid_q15
                0x00000000       0x6c ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_hard_tanh_q15
                0x00000000       0x96 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_leaky_relu_q15
                0x00000000       0x2a ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_adv_relu_q15
                0x00000000       0x3a ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_sigmoid_q15
                0x00000000       0x10 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_tanh_q15
                0x00000000       0x10 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_mult_q15
                0x00000000       0x48 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_add_q15
                0x00000000       0x46 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_sub_q15
                0x00000000       0x46 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_multiple_add_q15
                0x00000000       0x5c ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_multiple_mult_q15
                0x00000000       0xb2 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_multiple_sub_q15
                0x00000000       0x72 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text.local_1_minor_z_q15
                0x00000000       0xb2 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .rodata.nnom_tanh_table_q15
                0x00000000      0x200 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .rodata.nnom_sigmoid_table_q15
                0x00000000      0x200 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .srodata.cst4  0x00000000        0x8 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .text          0x00000000        0x0 ./galaxy_sdk/drivers/src/hal_pdm.o
 .data          0x00000000        0x0 ./galaxy_sdk/drivers/src/hal_pdm.o
 .bss           0x00000000        0x0 ./galaxy_sdk/drivers/src/hal_pdm.o
 .text.hal_pdm_remove_dev
                0x00000000       0x18 ./galaxy_sdk/drivers/src/hal_pdm.o
 .text.hal_pdm_get_device
                0x00000000       0x22 ./galaxy_sdk/drivers/src/hal_pdm.o
 .text.hal_pdm_init
                0x00000000        0xc ./galaxy_sdk/drivers/src/hal_pdm.o
 .text.hal_pdm_start
                0x00000000        0xc ./galaxy_sdk/drivers/src/hal_pdm.o
 .text.hal_pdm_stop
                0x00000000        0xa ./galaxy_sdk/drivers/src/hal_pdm.o
 .text.hal_pdm_set_gain
                0x00000000       0x18 ./galaxy_sdk/drivers/src/hal_pdm.o
 .text.hal_pdm_finalize
                0x00000000       0x18 ./galaxy_sdk/drivers/src/hal_pdm.o
 .itext         0x00000000        0xc ./galaxy_sdk/drivers/src/hal_pdm.o
 .text          0x00000000        0x0 ./galaxy_sdk/bsp/src/intexc_riscv.o
 .data          0x00000000        0x0 ./galaxy_sdk/bsp/src/intexc_riscv.o
 .bss           0x00000000        0x0 ./galaxy_sdk/bsp/src/intexc_riscv.o
 .text.trap     0x00000000       0xc0 ./galaxy_sdk/bsp/src/intexc_riscv.o
 .text.irq      0x00000000       0x78 ./galaxy_sdk/bsp/src/intexc_riscv.o
 .debug_line    0x00000000       0xb0 ./galaxy_sdk/bsp/src/intexc_riscv.o
 .debug_line_str
                0x00000000       0x9d ./galaxy_sdk/bsp/src/intexc_riscv.o
 .debug_info    0x00000000       0x23 ./galaxy_sdk/bsp/src/intexc_riscv.o
 .debug_abbrev  0x00000000       0x12 ./galaxy_sdk/bsp/src/intexc_riscv.o
 .debug_aranges
                0x00000000       0x28 ./galaxy_sdk/bsp/src/intexc_riscv.o
 .debug_str     0x00000000       0xab ./galaxy_sdk/bsp/src/intexc_riscv.o
 .debug_rnglists
                0x00000000       0x1a ./galaxy_sdk/bsp/src/intexc_riscv.o
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/bsp/src/intexc_riscv.o
 .text          0x00000000        0x0 ./galaxy_sdk/bsp/src/portasm.o
 .data          0x00000000        0x0 ./galaxy_sdk/bsp/src/portasm.o
 .bss           0x00000000        0x0 ./galaxy_sdk/bsp/src/portasm.o
 .text.entry    0x00000000      0x100 ./galaxy_sdk/bsp/src/portasm.o
 .text          0x00000000        0x0 ./galaxy_sdk/bsp/src/qemu_board.o
 .data          0x00000000        0x0 ./galaxy_sdk/bsp/src/qemu_board.o
 .bss           0x00000000        0x0 ./galaxy_sdk/bsp/src/qemu_board.o
 .text          0x00000000        0x0 ./galaxy_sdk/bsp/src/startup_riscv.o
 .data          0x00000000        0x0 ./galaxy_sdk/bsp/src/startup_riscv.o
 .bss           0x00000000        0x0 ./galaxy_sdk/bsp/src/startup_riscv.o
 .text          0x00000000        0x0 ./galaxy_sdk/main.o
 .data          0x00000000        0x0 ./galaxy_sdk/main.o
 .bss           0x00000000        0x0 ./galaxy_sdk/main.o
 .text          0x00000000        0x0 ./galaxy_sdk/speaker_inference.o
 .data          0x00000000        0x0 ./galaxy_sdk/speaker_inference.o
 .bss           0x00000000        0x0 ./galaxy_sdk/speaker_inference.o
 .text.print_float
                0x00000000       0x36 ./galaxy_sdk/speaker_inference.o
 .text.preprocess_mfcc
                0x00000000       0x5e ./galaxy_sdk/speaker_inference.o
 .text.speaker_is_target
                0x00000000       0x1a ./galaxy_sdk/speaker_inference.o
 .rodata.speaker_get_name.str1.4
                0x00000000        0x8 ./galaxy_sdk/speaker_inference.o
 .text.speaker_get_name
                0x00000000       0x20 ./galaxy_sdk/speaker_inference.o
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .text.vPortEndScheduler
                0x00000000       0x30 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .rodata.vPortAssert.str1.4
                0x00000000       0x13 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .text.vPortAssert
                0x00000000       0x52 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .text.vPortYield
                0x00000000       0x2e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .text.get_systick_reload_diff_val
                0x00000000       0x22 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .text.get_os_tick_period_ms
                0x00000000        0x6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .text.vPortUpdateSysTickTimer
                0x00000000      0x128 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .text.xIsPrivileged
                0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .rodata.vApplicationMallocNonCacheFailedHook.str1.4
                0x00000000       0x17 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .text.vApplicationMallocNonCacheFailedHook
                0x00000000       0x10 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_reset_ctrl
                0x00000000      0x276 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.systimer_cnt_delay
                0x00000000       0xe4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_set_interrupt_level
                0x00000000       0x7a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_cpu_clock_get_freq
                0x00000000       0x1e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_ahb_clock_get_freq
                0x00000000       0x1e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_apb_clock_get_freq
                0x00000000       0x1e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .nonxip_text   0x00000000      0x18c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_qspi_secure_mode_ctrl
                0x00000000       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_usb_set_mode
                0x00000000       0x7e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_usb_enable_dma
                0x00000000       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_usb_reset
                0x00000000       0x4a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_adc_sw_reset
                0x00000000       0x46 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_vdt_reset
                0x00000000       0x34 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_dmac_ch_mux_cfg
                0x00000000       0x72 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_dmac_ch_mux_free
                0x00000000       0x5a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_io_ctrl_pu
                0x00000000       0x74 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_io_ctrl_pd
                0x00000000       0x70 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_gpio_to_irq_id
                0x00000000        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_platform_in_isr
                0x00000000       0x16 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_sw_reset
                0x00000000        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_sysctl_init
                0x00000000       0x4e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_post_init
                0x00000000       0x7e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_sw_reset_wdt
                0x00000000        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_sw_stop_zsp
                0x00000000       0x22 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_sw_load_zsp
                0x00000000       0x72 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_sw_start_zsp
                0x00000000       0x34 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.soc_get_zsp_boot_status
                0x00000000        0xc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.delay_us
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text.delay_ms
                0x00000000       0x10 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .sdram_data    0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .text.soc_pinmux_init
                0x00000000       0xa4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .text.soc_pinmux_config
                0x00000000       0x52 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .text.soc_pinmux_set
                0x00000000       0x44 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .text.soc_pinpad_config
                0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .rodata.jtag_mux_ctrl
                0x00000000       0x20 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .debug_info    0x00000000      0x70a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .debug_abbrev  0x00000000      0x238 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .debug_loclists
                0x00000000      0x314 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .debug_aranges
                0x00000000       0x38 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .debug_rnglists
                0x00000000      0x180 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .debug_line    0x00000000      0x4bc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .debug_str     0x00000000      0x709 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .debug_line_str
                0x00000000      0x310 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .comment       0x00000000       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .debug_frame   0x00000000       0x50 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .riscv.attributes
                0x00000000       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_pinmux.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .rodata.system_exception_register.str1.4
                0x00000000       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .text.system_exception_register
                0x00000000       0x3e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .text.boot_get_fw_entry
                0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .text.boot_get_zsp_addr
                0x00000000       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .text.boot_image_load_common
                0x00000000      0x13a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .text.boot_image_load_xip
                0x00000000       0xc2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .text.boot_set_ota_stage2_info
                0x00000000       0x98 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .text.boot_ota_stage2_copy
                0x00000000      0x148 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .debug_info    0x00000000      0xc1c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .debug_abbrev  0x00000000      0x27c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .debug_loclists
                0x00000000      0xbee D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .debug_aranges
                0x00000000       0x48 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .debug_rnglists
                0x00000000       0xa0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .debug_line    0x00000000      0xb7b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .debug_str     0x00000000      0x842 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .debug_line_str
                0x00000000      0x42a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .comment       0x00000000       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .debug_frame   0x00000000      0x1b8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .riscv.attributes
                0x00000000       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(boot.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .text.board_find_device_by_id
                0x00000000       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .text.get_wakeup_src
                0x00000000       0x12 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .text.cpu_idle
                0x00000000        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .text.cpu_halt
                0x00000000        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .text.cpu_disable_interrupts
                0x00000000        0x6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .text.cpu_mem_barrier
                0x00000000        0xa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .text.cpu_flush_dcache
                0x00000000        0x6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .text.cpu_get_systick_remain_time_us
                0x00000000       0xa0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text.propagate_child_rate
                0x00000000       0xa4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text._clk_enable
                0x00000000       0x70 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text.clk_create
                0x00000000      0x1c4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text.clk_set_rate
                0x00000000       0xa0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text.clk_get_ref
                0x00000000       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text.clk_enable
                0x00000000       0x86 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text.clk_get_enable
                0x00000000       0x18 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text.clk_set_parent
                0x00000000       0xc8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text.clk_get_parent
                0x00000000       0x2c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text.clk_get_name
                0x00000000       0x18 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .text.pdm_simulator_device_release
                0x00000000       0x46 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .text.prints   0x00000000      0x14c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .text.printi   0x00000000       0xb6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .text.print_inner
                0x00000000      0x164 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .rodata.uart_sprintf.str1.4
                0x00000000        0x7 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .text.uart_sprintf
                0x00000000      0x31a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .rodata.uart_sprintf
                0x00000000      0x108 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .text.uart_debug_init
                0x00000000       0x1e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .text.vpi_timer_init
                0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .rodata.vpi_timer_reset.str1.4
                0x00000000       0x11 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .text.vpi_timer_reset
                0x00000000       0x38 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .rodata.vpi_timer_start_from_isr.str1.4
                0x00000000       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .text.vpi_timer_start_from_isr
                0x00000000       0x38 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .rodata.vpi_timer_stop_from_isr.str1.4
                0x00000000       0x19 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .text.vpi_timer_stop_from_isr
                0x00000000       0x38 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .rodata.vpi_timer_reset_from_isr.str1.4
                0x00000000       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .text.vpi_timer_reset_from_isr
                0x00000000       0x38 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .rodata.vpi_error_print.str1.4
                0x00000000       0xc7 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .text.vpi_error_print
                0x00000000      0x14a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .rodata.vpi_error_print
                0x00000000       0x34 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .text.vsa_to_vpi
                0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .text.set_logging_level
                0x00000000        0xa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text.osal_end_scheduler
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text.osal_suspend_task
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text.osal_suspend_all
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text.osal_resume_task
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text.osal_resume_all
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text.osal_sleep
                0x00000000       0x24 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text.osal_started
                0x00000000        0xe D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text.osal_enter_critical
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text.osal_exit_critical
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text.osal_highest_priority
                0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .text.osal_tick_period_ms
                0x00000000        0x6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .text.osal_tick_to_ms
                0x00000000        0xc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .text.osal_get_uptime
                0x00000000       0x1c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .text.osal_get_uptime_us
                0x00000000       0x18 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .text.osal_setup_timer
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .text.osal_update_systick
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .text.osal_timer_start_isr
                0x00000000       0x6c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .text.osal_timer_reset
                0x00000000       0x4a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .text.osal_timer_reset_isr
                0x00000000       0x6c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .text.osal_timer_stop_isr
                0x00000000       0x5c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .rodata.osal_dump_os_state.str1.4
                0x00000000       0xe7 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .text.osal_dump_os_state
                0x00000000      0x270 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .rodata.osal_dump_heap_size.str1.4
                0x00000000       0x19 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .text.osal_dump_heap_size
                0x00000000       0x32 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .text.osal_get_free_heap
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .srodata.CSWTCH.11
                0x00000000        0x5 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .text.osal_malloc_noncache
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .text.osal_free_noncache
                0x00000000        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.prvSearchForNameWithinSingleList
                0x00000000       0x62 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.eTaskGetState
                0x00000000       0xcc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.uxTaskPriorityGet
                0x00000000       0x42 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.uxTaskPriorityGetFromISR
                0x00000000       0x62 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskPrioritySet
                0x00000000      0x128 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskResume
                0x00000000       0xe4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskResumeFromISR
                0x00000000       0xfe D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskEndScheduler
                0x00000000       0x24 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskGetTickCountFromISR
                0x00000000       0x3e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.uxTaskGetNumberOfTasks
                0x00000000        0xa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskGetIdleTaskHandle
                0x00000000       0x22 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskCatchUpTicks
                0x00000000       0x5c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskGetHandle
                0x00000000       0xfc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskDelay
                0x00000000       0xc4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskDelayUntil
                0x00000000       0xfc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskSuspend
                0x00000000      0x14a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskPlaceOnUnorderedEventList
                0x00000000       0x70 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskRemoveFromUnorderedEventList
                0x00000000       0xae D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskSetTimeOutState
                0x00000000       0x4a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.uxTaskGetTaskNumber
                0x00000000        0xa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskSetTaskNumber
                0x00000000        0x6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskSetThreadLocalStoragePointer
                0x00000000       0x30 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.pvTaskGetThreadLocalStoragePointer
                0x00000000       0x24 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskGetInfo
                0x00000000       0xf2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.prvListTasksWithinSingleList.part.0
                0x00000000       0x8e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.uxTaskGetSystemState
                0x00000000      0x14c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.uxTaskGetStackHighWaterMark
                0x00000000       0x42 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskGetCurrentTaskHandle
                0x00000000        0xa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskPriorityInherit
                0x00000000       0xac D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskPriorityDisinheritAfterTimeout
                0x00000000       0xce D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .rodata.vTaskList.str1.4
                0x00000000        0xf D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskList
                0x00000000      0x152 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .rodata.vTaskGetRunTimeStats.str1.4
                0x00000000       0x18 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskGetRunTimeStats
                0x00000000      0x16c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.uxTaskResetEventItemValue
                0x00000000       0x1c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.pvTaskIncrementMutexHeldCount
                0x00000000       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.ulTaskGenericNotifyTake
                0x00000000       0xd0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskGenericNotifyWait
                0x00000000       0xfa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskGenericNotify
                0x00000000      0x168 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .rodata.xTaskGenericNotify
                0x00000000       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskGenericNotifyFromISR
                0x00000000      0x184 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .rodata.xTaskGenericNotifyFromISR
                0x00000000       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.vTaskGenericNotifyGiveFromISR
                0x00000000      0x120 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskGenericNotifyStateClear
                0x00000000       0x84 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.ulTaskGenericNotifyValueClear
                0x00000000       0x4c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.ulTaskGetIdleRunTimeCounter
                0x00000000        0xc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .srodata.CSWTCH.245
                0x00000000        0x5 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.xTimerCreateStatic
                0x00000000       0xac D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.xTimerGetTimerDaemonTaskHandle
                0x00000000       0x22 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.xTimerGetPeriod
                0x00000000       0x1c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.vTimerSetReloadMode
                0x00000000       0x62 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.uxTimerGetReloadMode
                0x00000000       0x42 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.xTimerGetExpiryTime
                0x00000000       0x1c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.pcTimerGetName
                0x00000000       0x1c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.xTimerIsTimerActive
                0x00000000       0x40 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.vTimerSetTimerID
                0x00000000       0x3e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.xTimerPendFunctionCallFromISR
                0x00000000       0x30 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.xTimerPendFunctionCall
                0x00000000       0x4c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.uxTimerGetTimerNumber
                0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.vTimerSetTimerNumber
                0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .text.xPortGetFreeHeapSize
                0x00000000        0xa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .text.xPortGetMinimumEverFreeHeapSize
                0x00000000        0xa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .text.vPortInitialiseBlocks
                0x00000000        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .text.vPortGetHeapStats
                0x00000000       0xac D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueGenericCreate
                0x00000000       0xce D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueCreateCountingSemaphoreStatic
                0x00000000       0x56 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueCreateCountingSemaphore
                0x00000000       0x52 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueCreateMutexStatic
                0x00000000       0x90 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueCreateMutex
                0x00000000       0x60 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueGiveFromISR
                0x00000000      0x108 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueSemaphoreTake
                0x00000000      0x280 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueuePeek
                0x00000000      0x224 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueReceiveFromISR
                0x00000000      0x118 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueuePeekFromISR
                0x00000000       0xca D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.uxQueueMessagesWaiting
                0x00000000       0x3c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.uxQueueSpacesAvailable
                0x00000000       0x40 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.uxQueueMessagesWaitingFromISR
                0x00000000       0x1c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.vQueueDelete
                0x00000000       0x28 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.uxQueueGetQueueNumber
                0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.vQueueSetQueueNumber
                0x00000000        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.ucQueueGetQueueType
                0x00000000        0x6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueIsQueueEmptyFromISR
                0x00000000       0x20 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueIsQueueFullFromISR
                0x00000000       0x24 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueCreateSet
                0x00000000       0x9e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueAddToSet
                0x00000000       0x3e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueRemoveFromSet
                0x00000000       0x38 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueSelectFromSet
                0x00000000       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueSelectFromSetFromISR
                0x00000000       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-atexit.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-atexit.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-atexit.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-calloc.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-calloc.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-calloc.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-callocr.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-callocr.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-callocr.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-malloc.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-malloc.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-malloc.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mlock.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mlock.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mlock.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-printf.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-printf.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-printf.o)
 .text._printf_r
                0x00000000       0x26 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-printf.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putchar.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putchar.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putchar.o)
 .text._putchar_r
                0x00000000        0xa D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putchar.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-snprintf.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-snprintf.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-snprintf.o)
 .text._snprintf_r
                0x00000000       0x78 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-snprintf.o)
 .text.snprintf
                0x00000000       0x7e D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-snprintf.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-snprintf.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-snprintf.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-snprintf.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sprintf.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sprintf.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sprintf.o)
 .text._sprintf_r
                0x00000000       0x42 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sprintf.o)
 .text.sprintf  0x00000000       0x4e D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sprintf.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sprintf.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sprintf.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sprintf.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-vprintf.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-vprintf.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-vprintf.o)
 .text._vprintf_r
                0x00000000        0xe D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-vprintf.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wbuf.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wbuf.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wbuf.o)
 .text.__swbuf  0x00000000       0x14 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wbuf.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wsetup.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wsetup.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wsetup.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strnlen.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strnlen.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strnlen.o)
 .text.strnlen  0x00000000       0x18 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strnlen.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strnlen.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strnlen.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strnlen.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stpcpy.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stpcpy.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stpcpy.o)
 .text.stpcpy   0x00000000       0x14 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stpcpy.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stpcpy.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stpcpy.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stpcpy.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-impure.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-impure.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-impure.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sbrkr.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sbrkr.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sbrkr.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-init.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-init.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-init.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fini.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fini.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fini.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x2 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x2 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x2 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x2 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x2 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .sbss.__lock___arc4random_mutex
                0x00000000        0x1 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .sbss.__lock___dd_hash_mutex
                0x00000000        0x1 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .sbss.__lock___tz_mutex
                0x00000000        0x1 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .sbss.__lock___env_recursive_mutex
                0x00000000        0x1 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .sbss.__lock___at_quick_exit_mutex
                0x00000000        0x1 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memset.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memset.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memcpy.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memcpy.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memcpy.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strlen.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strlen.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strlen.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o)
 .sdata.__atexit_dummy
                0x00000000        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__call_atexit.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__call_atexit.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__call_atexit.o)
 .text.__call_exitprocs
                0x00000000       0xea D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__call_atexit.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-freer.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-freer.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-freer.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
 .text.__ssputs_r
                0x00000000      0x100 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
 .text.__ssprint_r
                0x00000000      0x146 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
 .rodata._svfprintf_r.str1.4
                0x00000000       0x13 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
 .text._svfprintf_r
                0x00000000      0x296 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-svfprintf.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
 .text.__sprint_r
                0x00000000       0x28 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
 .text.vfprintf
                0x00000000       0x16 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o)
 .text.fflush   0x00000000       0x34 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .text.__fp_lock
                0x00000000       0x2a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .text.__fp_unlock
                0x00000000       0x2a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .text.__sfp    0x00000000       0xf2 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .text.__fp_lock_all
                0x00000000       0x2a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .text.__fp_unlock_all
                0x00000000       0x2a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fvwrite.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fvwrite.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fvwrite.o)
 .text.__sfvwrite_r
                0x00000000      0x306 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fvwrite.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fvwrite.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fvwrite.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fvwrite.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fwalk.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fwalk.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fwalk.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putc.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putc.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putc.o)
 .text.putc     0x00000000       0x14 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putc.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
 .text.__seofread
                0x00000000        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memchr.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memchr.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memchr.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-closer.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-closer.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-closer.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reent.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reent.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reent.o)
 .text._reclaim_reent
                0x00000000       0xfc D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reent.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fstatr.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fstatr.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fstatr.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-isattyr.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-isattyr.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-isattyr.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lseekr.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lseekr.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lseekr.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-readr.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-readr.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-readr.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-writer.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-writer.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-writer.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memmove-stub.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memmove-stub.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memmove-stub.o)
 .text.memmove  0x00000000       0x4a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memmove-stub.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memmove-stub.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memmove-stub.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memmove-stub.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reallocr.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reallocr.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reallocr.o)
 .text._realloc_r
                0x00000000       0x98 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reallocr.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reallocr.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reallocr.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reallocr.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-msizer.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-msizer.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-msizer.o)
 .text._malloc_usable_size_r
                0x00000000       0x14 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-msizer.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-msizer.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-msizer.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-msizer.o)
 .text          0x00000000      0x39a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_divdi3.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_divdi3.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_divdi3.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_divdi3.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_divdi3.o)
 .eh_frame      0x00000000       0x28 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_divdi3.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_divdi3.o)
 .text          0x00000000      0x35a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_udivdi3.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_udivdi3.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_udivdi3.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_udivdi3.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_udivdi3.o)
 .eh_frame      0x00000000       0x28 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_udivdi3.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_udivdi3.o)
 .text          0x00000000      0x322 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_umoddi3.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_umoddi3.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_umoddi3.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_umoddi3.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_umoddi3.o)
 .eh_frame      0x00000000       0x28 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_umoddi3.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_umoddi3.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_clz.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_clz.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_clz.o)
 .rodata        0x00000000      0x100 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_clz.o)
 .comment       0x00000000       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_clz.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_clz.o)
 .riscv.attributes
                0x00000000       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a(_clz.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_close.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_close.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_close.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fstat.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fstat.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fstat.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_isatty.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_isatty.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_isatty.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_lseek.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_lseek.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_lseek.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_open.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_open.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_open.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_read.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_read.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_read.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_sbrk.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_sbrk.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_sbrk.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_stat_common.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_stat_common.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_stat_common.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_write.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_write.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_write.o)
 .text          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .data          0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .bss           0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text.e203_uart_init
                0x00000000       0x26 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text.uart_config_stopbit
                0x00000000       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text.uart_write
                0x00000000       0x12 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text.uart_read
                0x00000000       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text.uart_set_tx_watermark
                0x00000000       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text.uart_enable_txint
                0x00000000       0x12 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text.uart_disable_txint
                0x00000000       0x10 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text.uart_set_rx_watermark
                0x00000000       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text.uart_enable_rxint
                0x00000000       0x12 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text.uart_disable_rxint
                0x00000000       0x10 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .debug_info    0x00000000      0x356 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .debug_abbrev  0x00000000      0x11d D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .debug_loclists
                0x00000000      0x384 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .debug_aranges
                0x00000000       0x68 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .debug_rnglists
                0x00000000       0x67 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .debug_line    0x00000000      0x4f2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .debug_str     0x00000000      0x2f1 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .debug_line_str
                0x00000000      0x26b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .comment       0x00000000       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .debug_frame   0x00000000       0xb0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .riscv.attributes
                0x00000000       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(e203_uart.c.o)
 .text          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-errno.o)
 .data          0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-errno.o)
 .bss           0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-errno.o)

Memory Configuration

Name             Origin             Length             Attributes
flash            0x20000000         0x00300000         axr!w
ram              0x90000000         0x00040000         axw!r
*default*        0x00000000         0xffffffff

Linker script and memory map

START GROUP
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o
LOAD ./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o
LOAD ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
LOAD ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
LOAD ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
LOAD ./galaxy_sdk/modules/external/nnom/src/core/nnom_utils.o
LOAD ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
LOAD ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
LOAD ./galaxy_sdk/drivers/src/hal_pdm.o
LOAD ./galaxy_sdk/bsp/src/intexc_riscv.o
LOAD ./galaxy_sdk/bsp/src/portasm.o
LOAD ./galaxy_sdk/bsp/src/qemu_board.o
LOAD ./galaxy_sdk/bsp/src/startup_riscv.o
LOAD ./galaxy_sdk/main.o
LOAD ./galaxy_sdk/speaker_inference.o
LOAD D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libstdc++.a
LOAD D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a
LOAD D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a
LOAD D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a
LOAD D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a
LOAD D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a
LOAD D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\libble.a
LOAD D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\libnmsis_dsp_rv32imafc_xxldsp.a
LOAD D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a
LOAD D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a
LOAD D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a
END GROUP
LOAD D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libstdc++.a
LOAD D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libm.a
LOAD D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a
START GROUP
LOAD D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc.a
LOAD D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libgloss.a
END GROUP
LOAD D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/rv32imafc_xxldsp/ilp32f\libgcc.a
                0x00000800                        PROVIDE (__STACK_SIZE = 0x800)
                0x00005000                        PROVIDE (__HEAP_SIZE = 0x5000)
                0x00000001                        PROVIDE (__SMP_CPU_CNT = 0x1)
                0x00000800                        __TOT_STACK_SIZE = (__STACK_SIZE * __SMP_CPU_CNT)

.init           0x20000000      0x308
 *(.vtable)
 .vtable        0x20000000      0x1a8 ./galaxy_sdk/bsp/src/startup_riscv.o
                0x20000000                vector_base
 *(SORT_NONE(.init))
 *fill*         0x200001a8       0x18 
 .init          0x200001c0      0x146 ./galaxy_sdk/bsp/src/startup_riscv.o
                0x200001c0                _start
                0x200002a6                _start_premain
                0x20000300                early_exc_entry
                0x20000308                        . = ALIGN (0x4)
 *fill*         0x20000306        0x2 

.text           0x20000340   0x1e62ac
 *(.text.unlikely .text.unlikely.*)
 *(.text.startup .text.startup.*)
 .text.startup.main
                0x20000340       0x64 ./galaxy_sdk/main.o
                0x20000340                main
 .text.startup.init_semihosting
                0x200003a4       0x50 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
                0x200003a4                init_semihosting
 *(.text .text.*)
 .text.relu_run
                0x200003f4       0x40 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .text.act_relu
                0x20000434       0x20 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
                0x20000434                act_relu
 .text.act_get_dec_bit
                0x20000454       0x10 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
                0x20000454                act_get_dec_bit
 .text.avgpool_build
                0x20000464       0x10 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
                0x20000464                avgpool_build
 .text.avgpool_run
                0x20000474       0x8a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
                0x20000474                avgpool_run
 .text.avgpool_s
                0x200004fe       0xf6 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
                0x200004fe                avgpool_s
 .text.default_build
                0x200005f4       0x52 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
                0x200005f4                default_build
 .text.conv2d_run
                0x20000646       0xc6 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
                0x20000646                conv2d_run
 .text.conv2d_free
                0x2000070c       0x26 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
                0x2000070c                conv2d_free
 .text.conv2d_build
                0x20000732      0x15a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
                0x20000732                conv2d_build
 .text.conv2d_s
                0x2000088c      0x170 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
                0x2000088c                conv2d_s
 .text.dense_build
                0x200009fc       0xd8 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
                0x200009fc                dense_build
 .text.dense_run
                0x20000ad4       0x80 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
                0x20000ad4                dense_run
 .text.dense_free
                0x20000b54       0x26 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
                0x20000b54                dense_free
 .text.dense_s  0x20000b7a       0x88 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
                0x20000b7a                dense_s
 .text.flatten_run
                0x20000c02        0x4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
                0x20000c02                flatten_run
 .text.flatten_build
                0x20000c06       0x58 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
                0x20000c06                flatten_build
 .text.Flatten  0x20000c5e       0x54 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
                0x20000c5e                Flatten
 .text.flatten_s
                0x20000cb2       0x16 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
                0x20000cb2                flatten_s
 .text.input_build
                0x20000cc8       0x3c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
                0x20000cc8                input_build
 .text.input_run
                0x20000d04       0x2c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
                0x20000d04                input_run
 .text.input_s  0x20000d30       0xf8 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
                0x20000d30                input_s
 .text.maxpool_build
                0x20000e28       0xd4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
                0x20000e28                maxpool_build
 .text.maxpool_run
                0x20000efc       0x6c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
                0x20000efc                maxpool_run
 .text.MaxPool  0x20000f68       0xce ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
                0x20000f68                MaxPool
 .text.maxpool_s
                0x20001036       0xae ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
                0x20001036                maxpool_s
 .text.output_run
                0x200010e4       0x30 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
                0x200010e4                output_run
 .text.output_s
                0x20001114       0x3e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
                0x20001114                output_s
 .text.softmax_build
                0x20001152       0x4a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
                0x20001152                softmax_build
 .text.softmax_run
                0x2000119c       0x32 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
                0x2000119c                softmax_run
 .text.Softmax  0x200011ce       0x52 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
                0x200011ce                Softmax
 .text.softmax_s
                0x20001220       0x16 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
                0x20001220                softmax_s
 .text.model_active
                0x20001236        0x6 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.print_memory_block_info
                0x2000123c       0x80 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.model_add
                0x200012bc       0x38 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.model_hook
                0x200012f4       0xb4 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.model_mergex
                0x200013a8       0x4c ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.model_merge
                0x200013f4        0x8 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .text.nnom_mem
                0x200013fc       0x34 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x200013fc                nnom_mem
 .text.nnom_alignto
                0x20001430        0xc ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x20001430                nnom_alignto
 .text.new_model
                0x2000143c       0x74 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x2000143c                new_model
 .text.compile_layers
                0x200014b0      0x59c ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x200014b0                compile_layers
 .text.tensor_mem_set
                0x20001a4c       0x2c ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x20001a4c                tensor_mem_set
 .text.set_tailed_activation
                0x20001a78       0x30 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x20001a78                set_tailed_activation
 .text.model_compile
                0x20001aa8      0x1a8 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x20001aa8                model_compile
 .text.model_run_to
                0x20001c50       0xbc ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x20001c50                model_run_to
 .text.model_run
                0x20001d0c        0x4 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x20001d0c                model_run
 .text.check_model_version
                0x20001d10       0x6c ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x20001d10                check_model_version
 .text.shape    0x20001d7c       0x16 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
                0x20001d7c                shape
 .text.kernel   0x20001d92       0x12 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
                0x20001d92                kernel
 .text.stride   0x20001da4       0x12 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
                0x20001da4                stride
 .text.dilation
                0x20001db6       0x12 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
                0x20001db6                dilation
 .text.io_init  0x20001dc8        0x6 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
                0x20001dc8                io_init
 .text.tensor_size
                0x20001dce       0x2c ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
                0x20001dce                tensor_size
 .text.tensor_get_num_channel
                0x20001dfa       0x10 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
                0x20001dfa                tensor_get_num_channel
 .text.new_tensor
                0x20001e0a       0x98 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
                0x20001e0a                new_tensor
 .text.delete_tensor
                0x20001ea2        0x8 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
                0x20001ea2                delete_tensor
 .text.tensor_set_attr_v
                0x20001eaa       0x42 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
                0x20001eaa                tensor_set_attr_v
 .text.tensor_set_attr
                0x20001eec       0x6a ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
                0x20001eec                tensor_set_attr
 .text.tensor_cpy_attr
                0x20001f56       0x7a ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
                0x20001f56                tensor_cpy_attr
 .text.local_avepool_q7_HWC
                0x20001fd0      0x192 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
                0x20001fd0                local_avepool_q7_HWC
 .text.local_maxpool_q7_HWC
                0x20002162      0x188 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
                0x20002162                local_maxpool_q7_HWC
 .text.local_convolve_HWC_q7_nonsquare
                0x200022ea      0x27e ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
                0x200022ea                local_convolve_HWC_q7_nonsquare
 .text.local_fully_connected_q7_opt
                0x20002568      0x2f6 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
                0x20002568                local_fully_connected_q7_opt
 .text.local_softmax_q7
                0x2000285e       0xc2 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
                0x2000285e                local_softmax_q7
 .text.local_relu_q7
                0x20002920       0x18 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
                0x20002920                local_relu_q7
 .text.local_avepool_q15_HWC
                0x20002938      0x196 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
                0x20002938                local_avepool_q15_HWC
 .text.local_convolve_HWC_q15_nonsquare
                0x20002ace      0x2ce ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
                0x20002ace                local_convolve_HWC_q15_nonsquare
 .text.local_relu_q15
                0x20002d9c       0x1c ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
                0x20002d9c                local_relu_q15
 .text.hal_pdm_add_dev
                0x20002db8       0x16 ./galaxy_sdk/drivers/src/hal_pdm.o
                0x20002db8                hal_pdm_add_dev
 *fill*         0x20002dce       0x32 
 .text.trap     0x20002e00       0x7e ./galaxy_sdk/bsp/src/portasm.o
                0x20002e00                exc_entry
 *fill*         0x20002e7e        0x2 
 .text.irq      0x20002e80      0x25a ./galaxy_sdk/bsp/src/portasm.o
                0x20002e80                irq_entry
                0x20002efa                default_intexc_handler
                0x20002f00                prvPortStartFirstTask
                0x20002fa4                eclic_msip_handler
 .text.qemu_board_find_device
                0x200030da        0x4 ./galaxy_sdk/bsp/src/qemu_board.o
                0x200030da                qemu_board_find_device
 .text.board_get_ops
                0x200030de        0xa ./galaxy_sdk/bsp/src/qemu_board.o
                0x200030de                board_get_ops
 .text.task_init_app
                0x200030e8       0xa2 ./galaxy_sdk/main.o
 .text.task_speaker_recognition
                0x2000318a       0x8a ./galaxy_sdk/main.o
 .text.nnom_model_create
                0x20003214      0x15a ./galaxy_sdk/speaker_inference.o
                0x20003214                nnom_model_create
 .text.speaker_model_init
                0x2000336e       0x62 ./galaxy_sdk/speaker_inference.o
                0x2000336e                speaker_model_init
 .text.postprocess_output
                0x200033d0       0x4a ./galaxy_sdk/speaker_inference.o
                0x200033d0                postprocess_output
 .text.speaker_inference
                0x2000341a      0x2ca ./galaxy_sdk/speaker_inference.o
                0x2000341a                speaker_inference
 .text.speaker_test
                0x200036e4       0x8e ./galaxy_sdk/speaker_inference.o
                0x200036e4                speaker_test
 .text.prvTaskExitError
                0x20003772       0x48 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .text.pxPortInitialiseStack
                0x200037ba       0x46 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x200037ba                pxPortInitialiseStack
 .text.vPortEnterCritical
                0x20003800       0x40 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x20003800                vPortEnterCritical
 .text.vPortExitCritical
                0x20003840       0x36 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x20003840                vPortExitCritical
 .text.xPortTaskSwitch
                0x20003876       0x64 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x20003876                xPortTaskSwitch
 .text.eclic_mtip_handler
                0x200038da      0x114 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x200038da                eclic_mtip_handler
 .text.vPortSetupTimerInterrupt
                0x200039ee      0x1f4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x200039ee                vPortSetupTimerInterrupt
 .text.xPortStartScheduler
                0x20003be2       0xfc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x20003be2                xPortStartScheduler
 .text.vPortValidateInterruptPriority
                0x20003cde       0x88 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x20003cde                vPortValidateInterruptPriority
 .text.vApplicationIdleHook
                0x20003d66        0xa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x20003d66                vApplicationIdleHook
 .text.vApplicationStackOverflowHook
                0x20003d70        0xc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x20003d70                vApplicationStackOverflowHook
 .text.vApplicationGetIdleTaskMemory
                0x20003d7c       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x20003d7c                vApplicationGetIdleTaskMemory
 .text.vApplicationGetTimerTaskMemory
                0x20003d90       0x18 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x20003d90                vApplicationGetTimerTaskMemory
 .text.vApplicationMallocFailedHook
                0x20003da8        0xc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x20003da8                vApplicationMallocFailedHook
 .text.soc_init
                0x20003db4        0xa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
                0x20003db4                soc_init
 .text.soc_rtc_clock_get_freq
                0x20003dbe       0x18 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
                0x20003dbe                soc_rtc_clock_get_freq
 .text.system_default_exception_handler
                0x20003dd6       0x64 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .text.premain_system_init
                0x20003e3a        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
                0x20003e3a                premain_system_init
 .text.core_exception_handler
                0x20003e3c       0x56 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
                0x20003e3c                core_exception_handler
 .text._premain_init
                0x20003e92       0x60 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
                0x20003e92                _premain_init
 .text._postmain_fini
                0x20003ef2        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
                0x20003ef2                _postmain_fini
 .text._init    0x20003ef4        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
                0x20003ef4                _init
 .text._fini    0x20003ef6        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
                0x20003ef6                _fini
 .text.board_register
                0x20003ef8       0x12 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
                0x20003ef8                board_register
 .text.board_init
                0x20003f0a       0x18 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
                0x20003f0a                board_init
 .text.clk_get_rate
                0x20003f22       0x2c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
                0x20003f22                clk_get_rate
 .text.pdm_deinit
                0x20003f4e       0x26 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .text.timer_handler
                0x20003f74      0x106 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .text.pdm_init
                0x2000407a       0x4c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .text.pdm_stop
                0x200040c6       0x68 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .text.pdm_start
                0x2000412e      0x174 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .text.pdm_simulator_device_init
                0x200042a2       0x66 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
                0x200042a2                pdm_simulator_device_init
 .text.uart_printf
                0x20004308       0x3c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
                0x20004308                uart_printf
 .text.vpi_timer_create
                0x20004344       0x66 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
                0x20004344                vpi_timer_create
 .text.vpi_timer_start
                0x200043aa       0x2c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
                0x200043aa                vpi_timer_start
 .text.vpi_timer_stop
                0x200043d6       0x2c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
                0x200043d6                vpi_timer_stop
 .text.vpi_timer_delete
                0x20004402       0x2c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
                0x20004402                vpi_timer_delete
 .text.vsd_to_vpi
                0x2000442e       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
                0x2000442e                vsd_to_vpi
 .text.osal_create_task
                0x20004448       0x4e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
                0x20004448                osal_create_task
 .text.osal_delete_task
                0x20004496        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
                0x20004496                osal_delete_task
 .text.osal_pre_start_scheduler
                0x20004498        0x2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
                0x20004498                osal_pre_start_scheduler
 .text.osal_start_scheduler
                0x2000449a        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
                0x2000449a                osal_start_scheduler
 .text.osal_ms_to_tick
                0x2000449e       0x68 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
                0x2000449e                osal_ms_to_tick
 .text.osal_timer_common_cb
                0x20004506       0x1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .text.osal_timer_create
                0x20004520       0x6e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
                0x20004520                osal_timer_create
 .text.osal_timer_delete
                0x2000458e       0x4a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
                0x2000458e                osal_timer_delete
 .text.osal_timer_start
                0x200045d8       0x3c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
                0x200045d8                osal_timer_start
 .text.osal_timer_stop
                0x20004614       0x30 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
                0x20004614                osal_timer_stop
 .text.osal_get_task_name
                0x20004644        0x6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
                0x20004644                osal_get_task_name
 .text.osal_malloc
                0x2000464a        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
                0x2000464a                osal_malloc
 .text.osal_free
                0x2000464e        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
                0x2000464e                osal_free
 .text.prvAddNewTaskToReadyList
                0x20004652      0x170 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.prvAddCurrentTaskToDelayedList
                0x200047c2       0xe0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.prvInitialiseNewTask.constprop.0
                0x200048a2       0xdc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.prvIdleTask
                0x2000497e       0xd6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskCreateStatic
                0x20004a54       0x7e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20004a54                xTaskCreateStatic
 .text.xTaskCreate
                0x20004ad2       0x7c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20004ad2                xTaskCreate
 .text.vTaskDelete
                0x20004b4e      0x148 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20004b4e                vTaskDelete
 .text.vTaskStartScheduler
                0x20004c96       0xa0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20004c96                vTaskStartScheduler
 .text.vTaskSuspendAll
                0x20004d36       0x10 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20004d36                vTaskSuspendAll
 .text.xTaskGetTickCount
                0x20004d46       0x20 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20004d46                xTaskGetTickCount
 .text.pcTaskGetName
                0x20004d66       0x28 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20004d66                pcTaskGetName
 .text.xTaskIncrementTick
                0x20004d8e      0x1b0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20004d8e                xTaskIncrementTick
 .text.xTaskResumeAll.part.0
                0x20004f3e      0x14e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .text.xTaskResumeAll
                0x2000508c       0x24 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x2000508c                xTaskResumeAll
 .text.vTaskSwitchContext
                0x200050b0       0xf2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x200050b0                vTaskSwitchContext
 .text.vTaskPlaceOnEventList
                0x200051a2       0x3a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x200051a2                vTaskPlaceOnEventList
 .text.vTaskPlaceOnEventListRestricted
                0x200051dc       0x44 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x200051dc                vTaskPlaceOnEventListRestricted
 .text.xTaskRemoveFromEventList
                0x20005220       0x96 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20005220                xTaskRemoveFromEventList
 .text.vTaskInternalSetTimeOutState
                0x200052b6       0x16 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x200052b6                vTaskInternalSetTimeOutState
 .text.xTaskCheckForTimeOut
                0x200052cc       0x9a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x200052cc                xTaskCheckForTimeOut
 .text.vTaskMissedYield
                0x20005366        0xc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20005366                vTaskMissedYield
 .text.xTaskGetSchedulerState
                0x20005372       0x1c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20005372                xTaskGetSchedulerState
 .text.xTaskPriorityDisinherit
                0x2000538e       0x9c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x2000538e                xTaskPriorityDisinherit
 .text.vListInitialise
                0x2000542a       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
                0x2000542a                vListInitialise
 .text.vListInitialiseItem
                0x2000543e        0x6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
                0x2000543e                vListInitialiseItem
 .text.vListInsertEnd
                0x20005444       0x16 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
                0x20005444                vListInsertEnd
 .text.vListInsert
                0x2000545a       0x2e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
                0x2000545a                vListInsert
 .text.uxListRemove
                0x20005488       0x2e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
                0x20005488                uxListRemove
 .text.prvCheckForValidListAndQueue
                0x200054b6       0x7a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.xTimerCreateTimerTask
                0x20005530       0x64 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
                0x20005530                xTimerCreateTimerTask
 .text.xTimerCreate
                0x20005594       0x7e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
                0x20005594                xTimerCreate
 .text.xTimerGenericCommand
                0x20005612       0x84 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
                0x20005612                xTimerGenericCommand
 .text.prvSwitchTimerLists
                0x20005696       0x96 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.prvTimerTask
                0x2000572c      0x2bc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .text.pvTimerGetTimerID
                0x200059e8       0x34 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
                0x200059e8                pvTimerGetTimerID
 .text.prvInsertBlockIntoFreeList
                0x20005a1c       0x62 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .text.pvPortMalloc
                0x20005a7e      0x1a0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
                0x20005a7e                pvPortMalloc
 .text.vPortFree
                0x20005c1e       0x8c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
                0x20005c1e                vPortFree
 .text.prvCopyDataToQueue
                0x20005caa       0xa0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.prvNotifyQueueSetContainer
                0x20005d4a       0xba D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.prvUnlockQueue
                0x20005e04       0xc6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .text.xQueueGenericReset
                0x20005eca       0xbc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
                0x20005eca                xQueueGenericReset
 .text.xQueueGenericCreateStatic
                0x20005f86       0xc8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
                0x20005f86                xQueueGenericCreateStatic
 .text.xQueueGenericSend
                0x2000604e      0x220 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
                0x2000604e                xQueueGenericSend
 .text.xQueueGenericSendFromISR
                0x2000626e      0x13c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
                0x2000626e                xQueueGenericSendFromISR
 .text.xQueueReceive
                0x200063aa      0x1cc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
                0x200063aa                xQueueReceive
 .text.vQueueWaitForMessageRestricted
                0x20006576       0x6c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
                0x20006576                vQueueWaitForMessageRestricted
 .text.atexit   0x200065e2        0xa D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-atexit.o)
                0x200065e2                atexit
 .text.calloc   0x200065ec        0xa D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-calloc.o)
                0x200065ec                calloc
 .text._calloc_r
                0x200065f6       0x32 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-callocr.o)
                0x200065f6                _calloc_r
 .text.malloc   0x20006628        0x8 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-malloc.o)
                0x20006628                malloc
 .text.free     0x20006630        0xa D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-malloc.o)
                0x20006630                free
 .text.sbrk_aligned
                0x2000663a       0x58 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
 .text._malloc_r
                0x20006692      0x120 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
                0x20006692                _malloc_r
 .text.__malloc_lock
                0x200067b2        0xa D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mlock.o)
                0x200067b2                __malloc_lock
 .text.__malloc_unlock
                0x200067bc        0xa D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mlock.o)
                0x200067bc                __malloc_unlock
 .text.printf   0x200067c6       0x28 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-printf.o)
                0x200067c6                iprintf
                0x200067c6                printf
 .text.putchar  0x200067ee        0xc D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putchar.o)
                0x200067ee                putchar
 .text._puts_r  0x200067fa       0xce D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o)
                0x200067fa                _puts_r
 .text.puts     0x200068c8        0xa D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o)
                0x200068c8                puts
 .text.vprintf  0x200068d2        0xc D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-vprintf.o)
                0x200068d2                viprintf
                0x200068d2                vprintf
 .text.__swbuf_r
                0x200068de       0x9c D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wbuf.o)
                0x200068de                __swbuf_r
 .text.__swsetup_r
                0x2000697a       0xc8 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wsetup.o)
                0x2000697a                __swsetup_r
 .text._sbrk_r  0x20006a42       0x30 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sbrkr.o)
                0x20006a42                _sbrk_r
 .text.__libc_init_array
                0x20006a72       0x5e D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-init.o)
                0x20006a72                __libc_init_array
 .text.__libc_fini_array
                0x20006ad0       0x36 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fini.o)
                0x20006ad0                __libc_fini_array
 .text.__retarget_lock_init_recursive
                0x20006b06        0x2 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
                0x20006b06                __retarget_lock_init_recursive
 .text.__retarget_lock_acquire_recursive
                0x20006b08        0x2 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
                0x20006b08                __retarget_lock_acquire_recursive
 .text.__retarget_lock_release_recursive
                0x20006b0a        0x2 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
                0x20006b0a                __retarget_lock_release_recursive
 .text          0x20006b0c       0xa8 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memset.o)
                0x20006b0c                memset
 .text.memcpy   0x20006bb4       0xe8 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memcpy.o)
                0x20006bb4                memcpy
 .text.strlen   0x20006c9c       0x12 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strlen.o)
                0x20006c9c                strlen
 .text.__register_exitproc
                0x20006cae       0xba D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o)
                0x20006cae                __register_exitproc
 .text._free_r  0x20006d68       0xa6 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-freer.o)
                0x20006d68                _free_r
 .text.__sfputc_r
                0x20006e0e       0x2a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
 .text.__sfputs_r
                0x20006e38       0x44 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
                0x20006e38                __sfputs_r
 .text._vfprintf_r
                0x20006e7c      0x2ae D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
                0x20006e7c                _vfprintf_r
                0x20006e7c                _vfiprintf_r
 .text._printf_common
                0x2000712a      0x112 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
                0x2000712a                _printf_common
 .text._printf_i
                0x2000723c      0x262 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
                0x2000723c                _printf_i
 .text.__sflush_r
                0x2000749e      0x13c D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o)
                0x2000749e                __sflush_r
 .text._fflush_r
                0x200075da       0x60 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o)
                0x200075da                _fflush_r
 .text.std      0x2000763a       0x9c D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .text.stdio_exit_handler
                0x200076d6       0x1a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .text.cleanup_stdio
                0x200076f0       0x50 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .text.global_stdio_init.part.0
                0x20007740       0x48 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .text.__sfp_lock_acquire
                0x20007788        0xc D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
                0x20007788                __sfp_lock_acquire
 .text.__sfp_lock_release
                0x20007794        0xc D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
                0x20007794                __sfp_lock_release
 .text.__sinit  0x200077a0       0x34 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
                0x200077a0                __sinit
 .text._fwalk_sglue
                0x200077d4       0x68 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fwalk.o)
                0x200077d4                _fwalk_sglue
 .text.__swhatbuf_r
                0x2000783c       0x5a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o)
                0x2000783c                __swhatbuf_r
 .text.__smakebuf_r
                0x20007896       0x9c D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o)
                0x20007896                __smakebuf_r
 .text._putc_r  0x20007932       0x8c D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putc.o)
                0x20007932                _putc_r
 .text.__sread  0x200079be       0x30 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
                0x200079be                __sread
 .text.__swrite
                0x200079ee       0x4e D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
                0x200079ee                __swrite
 .text.__sseek  0x20007a3c       0x36 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
                0x20007a3c                __sseek
 .text.__sclose
                0x20007a72        0x6 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
                0x20007a72                __sclose
 .text.memchr   0x20007a78       0x1a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memchr.o)
                0x20007a78                memchr
 .text._close_r
                0x20007a92       0x2e D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-closer.o)
                0x20007a92                _close_r
 .text._fstat_r
                0x20007ac0       0x30 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fstatr.o)
                0x20007ac0                _fstat_r
 .text._isatty_r
                0x20007af0       0x2e D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-isattyr.o)
                0x20007af0                _isatty_r
 .text._lseek_r
                0x20007b1e       0x32 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lseekr.o)
                0x20007b1e                _lseek_r
 .text._read_r  0x20007b50       0x32 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-readr.o)
                0x20007b50                _read_r
 .text._write_r
                0x20007b82       0x32 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-writer.o)
                0x20007b82                _write_r
 *fill*         0x20007bb4        0xc 
 .text._close   0x20007bc0       0x6a D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_close.o)
                0x20007bc0                _close
 .text.__add_fdentry
                0x20007c2a       0x42 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
                0x20007c2a                __add_fdentry
 .text.__get_fdentry
                0x20007c6c       0x2e D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
                0x20007c6c                __get_fdentry
 .text.__remove_fdentry
                0x20007c9a       0x12 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
                0x20007c9a                __remove_fdentry
 .text._fstat   0x20007cac       0x20 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fstat.o)
                0x20007cac                _fstat
 *fill*         0x20007ccc        0x4 
 .text._isatty  0x20007cd0       0x64 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_isatty.o)
                0x20007cd0                _isatty
 *fill*         0x20007d34        0xc 
 .text._lseek   0x20007d40       0xc0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_lseek.o)
                0x20007d40                _lseek
 *fill*         0x20007e00        0x0 
 .text._open    0x20007e00       0xc2 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_open.o)
                0x20007e00                _open
 *fill*         0x20007ec2        0xe 
 .text._read    0x20007ed0       0x6e D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_read.o)
                0x20007ed0                _read
 .text._sbrk    0x20007f3e       0x22 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_sbrk.o)
                0x20007f3e                _sbrk
 *fill*         0x20007f60        0x0 
 .text.__stat_common
                0x20007f60       0x6c D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_stat_common.o)
                0x20007f60                __stat_common
 *fill*         0x20007fcc        0x4 
 .text._write   0x20007fd0       0x68 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_write.o)
                0x20007fd0                _write
 .text.__errno  0x20008038        0x6 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-errno.o)
                0x20008038                __errno
 *(.gnu.linkonce.t.*)
                0x20008040                        . = ALIGN (0x8)
 *fill*         0x2000803e        0x2 
 *(.srodata.cst16)
 *(.srodata.cst8)
 *(.srodata.cst4)
 .srodata.cst4  0x20008040       0x18 ./galaxy_sdk/speaker_inference.o
 *(.srodata.cst2)
 *(.srodata .srodata.*)
 .srodata.qemu_board_name
                0x20008058        0x5 ./galaxy_sdk/bsp/src/qemu_board.o
 *fill*         0x2000805d        0x3 
 .srodata.output0_config
                0x20008060        0x8 ./galaxy_sdk/speaker_inference.o
                0x20008060                output0_config
 .srodata.tensor_output0_offset
                0x20008068        0x4 ./galaxy_sdk/speaker_inference.o
                0x20008068                tensor_output0_offset
 .srodata.tensor_output0_dec
                0x2000806c        0x4 ./galaxy_sdk/speaker_inference.o
                0x2000806c                tensor_output0_dec
 .srodata.tensor_output0_dim
                0x20008070        0x2 ./galaxy_sdk/speaker_inference.o
                0x20008070                tensor_output0_dim
 *fill*         0x20008072        0x2 
 .srodata.softmax_config
                0x20008074        0x4 ./galaxy_sdk/speaker_inference.o
                0x20008074                softmax_config
 .srodata.dense_bias_shift
                0x20008078        0x4 ./galaxy_sdk/speaker_inference.o
                0x20008078                dense_bias_shift
 .srodata.dense_output_shift
                0x2000807c        0x4 ./galaxy_sdk/speaker_inference.o
                0x2000807c                dense_output_shift
 .srodata.tensor_dense_bias_0_offset
                0x20008080        0x4 ./galaxy_sdk/speaker_inference.o
                0x20008080                tensor_dense_bias_0_offset
 .srodata.tensor_dense_bias_0_dec
                0x20008084        0x4 ./galaxy_sdk/speaker_inference.o
                0x20008084                tensor_dense_bias_0_dec
 .srodata.tensor_dense_bias_0_dim
                0x20008088        0x2 ./galaxy_sdk/speaker_inference.o
                0x20008088                tensor_dense_bias_0_dim
 *fill*         0x2000808a        0x2 
 .srodata.tensor_dense_kernel_0_offset
                0x2000808c        0x4 ./galaxy_sdk/speaker_inference.o
                0x2000808c                tensor_dense_kernel_0_offset
 .srodata.tensor_dense_kernel_0_dec
                0x20008090        0x4 ./galaxy_sdk/speaker_inference.o
                0x20008090                tensor_dense_kernel_0_dec
 .srodata.tensor_dense_kernel_0_dim
                0x20008094        0x4 ./galaxy_sdk/speaker_inference.o
                0x20008094                tensor_dense_kernel_0_dim
 .srodata.flatten_config
                0x20008098        0x4 ./galaxy_sdk/speaker_inference.o
                0x20008098                flatten_config
 .srodata.conv2d_3_bias_shift
                0x2000809c        0x4 ./galaxy_sdk/speaker_inference.o
                0x2000809c                conv2d_3_bias_shift
 .srodata.conv2d_3_output_shift
                0x200080a0        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080a0                conv2d_3_output_shift
 .srodata.tensor_conv2d_3_bias_0_offset
                0x200080a4        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080a4                tensor_conv2d_3_bias_0_offset
 .srodata.tensor_conv2d_3_bias_0_dec
                0x200080a8        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080a8                tensor_conv2d_3_bias_0_dec
 .srodata.tensor_conv2d_3_bias_0_dim
                0x200080ac        0x2 ./galaxy_sdk/speaker_inference.o
                0x200080ac                tensor_conv2d_3_bias_0_dim
 *fill*         0x200080ae        0x2 
 .srodata.tensor_conv2d_3_kernel_0_offset
                0x200080b0        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080b0                tensor_conv2d_3_kernel_0_offset
 .srodata.tensor_conv2d_3_kernel_0_dec
                0x200080b4        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080b4                tensor_conv2d_3_kernel_0_dec
 .srodata.tensor_conv2d_3_kernel_0_dim
                0x200080b8        0x8 ./galaxy_sdk/speaker_inference.o
                0x200080b8                tensor_conv2d_3_kernel_0_dim
 .srodata.conv2d_2_bias_shift
                0x200080c0        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080c0                conv2d_2_bias_shift
 .srodata.conv2d_2_output_shift
                0x200080c4        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080c4                conv2d_2_output_shift
 .srodata.tensor_conv2d_2_bias_0_offset
                0x200080c8        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080c8                tensor_conv2d_2_bias_0_offset
 .srodata.tensor_conv2d_2_bias_0_dec
                0x200080cc        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080cc                tensor_conv2d_2_bias_0_dec
 .srodata.tensor_conv2d_2_bias_0_dim
                0x200080d0        0x2 ./galaxy_sdk/speaker_inference.o
                0x200080d0                tensor_conv2d_2_bias_0_dim
 *fill*         0x200080d2        0x2 
 .srodata.tensor_conv2d_2_kernel_0_offset
                0x200080d4        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080d4                tensor_conv2d_2_kernel_0_offset
 .srodata.tensor_conv2d_2_kernel_0_dec
                0x200080d8        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080d8                tensor_conv2d_2_kernel_0_dec
 .srodata.tensor_conv2d_2_kernel_0_dim
                0x200080dc        0x8 ./galaxy_sdk/speaker_inference.o
                0x200080dc                tensor_conv2d_2_kernel_0_dim
 .srodata.conv2d_1_bias_shift
                0x200080e4        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080e4                conv2d_1_bias_shift
 .srodata.conv2d_1_output_shift
                0x200080e8        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080e8                conv2d_1_output_shift
 .srodata.tensor_conv2d_1_bias_0_offset
                0x200080ec        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080ec                tensor_conv2d_1_bias_0_offset
 .srodata.tensor_conv2d_1_bias_0_dec
                0x200080f0        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080f0                tensor_conv2d_1_bias_0_dec
 .srodata.tensor_conv2d_1_bias_0_dim
                0x200080f4        0x2 ./galaxy_sdk/speaker_inference.o
                0x200080f4                tensor_conv2d_1_bias_0_dim
 *fill*         0x200080f6        0x2 
 .srodata.tensor_conv2d_1_kernel_0_offset
                0x200080f8        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080f8                tensor_conv2d_1_kernel_0_offset
 .srodata.tensor_conv2d_1_kernel_0_dec
                0x200080fc        0x4 ./galaxy_sdk/speaker_inference.o
                0x200080fc                tensor_conv2d_1_kernel_0_dec
 .srodata.tensor_conv2d_1_kernel_0_dim
                0x20008100        0x8 ./galaxy_sdk/speaker_inference.o
                0x20008100                tensor_conv2d_1_kernel_0_dim
 .srodata.input_config
                0x20008108        0x8 ./galaxy_sdk/speaker_inference.o
                0x20008108                input_config
 .srodata.tensor_input_offset
                0x20008110        0x4 ./galaxy_sdk/speaker_inference.o
                0x20008110                tensor_input_offset
 .srodata.tensor_input_dec
                0x20008114        0x4 ./galaxy_sdk/speaker_inference.o
                0x20008114                tensor_input_dec
 .srodata.tensor_input_dim
                0x20008118        0x6 ./galaxy_sdk/speaker_inference.o
                0x20008118                tensor_input_dim
 *fill*         0x2000811e        0x2 
 .srodata.uxTopUsedPriority
                0x20008120        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x20008120                uxTopUsedPriority
 *(.rdata)
 *(.rodata .rodata.*)
 .rodata.print_memory_block_info.str1.4
                0x20008124        0xc ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .rodata.model_add.str1.4
                0x20008130       0x44 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .rodata.compile_layers.str1.4
                0x20008174       0xff ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 *fill*         0x20008273        0x1 
 .rodata.mem_analysis_result.str1.4
                0x20008274       0x5d ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 *fill*         0x200082d1        0x3 
 .rodata.set_tailed_activation.str1.4
                0x200082d4       0x14 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .rodata.model_compile.str1.4
                0x200082e8      0x21b ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 *fill*         0x20008503        0x1 
 .rodata.model_run_to.str1.4
                0x20008504       0x63 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 *fill*         0x20008567        0x1 
 .rodata.check_model_version.str1.4
                0x20008568       0x5c ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .rodata.default_cell_names
                0x200085c4       0x20 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x200085c4                default_cell_names
 .rodata.default_activation_names
                0x200085e4       0x40 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x200085e4                default_activation_names
 .rodata.default_layer_names
                0x20008624      0x18c ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x20008624                default_layer_names
 .rodata.new_tensor.str1.4
                0x200087b0       0x21 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 *fill*         0x200087d1        0x3 
 .rodata.qemu_board_ops
                0x200087d4        0xc ./galaxy_sdk/bsp/src/qemu_board.o
                0x200087d4                qemu_board_ops
 .rodata.task_init_app.str1.4
                0x200087e0       0x89 ./galaxy_sdk/main.o
 *fill*         0x20008869        0x3 
 .rodata.task_speaker_recognition.str1.4
                0x2000886c       0xfe ./galaxy_sdk/main.o
 *fill*         0x2000896a        0x2 
 .rodata.main.str1.4
                0x2000896c       0x3d ./galaxy_sdk/main.o
 *fill*         0x200089a9        0x3 
 .rodata.print_float.str1.4
                0x200089ac        0x8 ./galaxy_sdk/speaker_inference.o
 .rodata.speaker_model_init.str1.4
                0x200089b4       0x8d ./galaxy_sdk/speaker_inference.o
 *fill*         0x20008a41        0x3 
 .rodata.speaker_inference.str1.4
                0x20008a44       0xe6 ./galaxy_sdk/speaker_inference.o
 *fill*         0x20008b2a        0x2 
 .rodata.speaker_test.str1.4
                0x20008b2c      0x125 ./galaxy_sdk/speaker_inference.o
 *fill*         0x20008c51        0x3 
 .rodata.is_target_speaker
                0x20008c54       0x10 ./galaxy_sdk/speaker_inference.o
 .rodata.str1.4
                0x20008c64       0xe2 ./galaxy_sdk/speaker_inference.o
 *fill*         0x20008d46        0x2 
 .rodata.speaker_names
                0x20008d48       0x40 ./galaxy_sdk/speaker_inference.o
 .rodata.real_mfcc_data
                0x20008d88     0x2520 ./galaxy_sdk/speaker_inference.o
 .rodata.tensor_output0
                0x2000b2a8       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000b2a8                tensor_output0
 .rodata.dense_config
                0x2000b2c0       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000b2c0                dense_config
 .rodata.tensor_dense_bias_0
                0x2000b2d8       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000b2d8                tensor_dense_bias_0
 .rodata.tensor_dense_bias_0_data
                0x2000b2f0       0x10 ./galaxy_sdk/speaker_inference.o
                0x2000b2f0                tensor_dense_bias_0_data
 .rodata.tensor_dense_kernel_0
                0x2000b300       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000b300                tensor_dense_kernel_0
 .rodata.tensor_dense_kernel_0_data
                0x2000b318      0x200 ./galaxy_sdk/speaker_inference.o
                0x2000b318                tensor_dense_kernel_0_data
 .rodata.average_pooling_final_config
                0x2000b518       0x10 ./galaxy_sdk/speaker_inference.o
                0x2000b518                average_pooling_final_config
 .rodata.conv2d_3_config
                0x2000b528       0x28 ./galaxy_sdk/speaker_inference.o
                0x2000b528                conv2d_3_config
 .rodata.tensor_conv2d_3_bias_0
                0x2000b550       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000b550                tensor_conv2d_3_bias_0
 .rodata.tensor_conv2d_3_bias_0_data
                0x2000b568       0x20 ./galaxy_sdk/speaker_inference.o
                0x2000b568                tensor_conv2d_3_bias_0_data
 .rodata.tensor_conv2d_3_kernel_0
                0x2000b588       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000b588                tensor_conv2d_3_kernel_0
 .rodata.tensor_conv2d_3_kernel_0_data
                0x2000b5a0     0x2400 ./galaxy_sdk/speaker_inference.o
                0x2000b5a0                tensor_conv2d_3_kernel_0_data
 .rodata.max_pooling_2_config
                0x2000d9a0       0x10 ./galaxy_sdk/speaker_inference.o
                0x2000d9a0                max_pooling_2_config
 .rodata.conv2d_2_config
                0x2000d9b0       0x28 ./galaxy_sdk/speaker_inference.o
                0x2000d9b0                conv2d_2_config
 .rodata.tensor_conv2d_2_bias_0
                0x2000d9d8       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000d9d8                tensor_conv2d_2_bias_0
 .rodata.tensor_conv2d_2_bias_0_data
                0x2000d9f0       0x20 ./galaxy_sdk/speaker_inference.o
                0x2000d9f0                tensor_conv2d_2_bias_0_data
 .rodata.tensor_conv2d_2_kernel_0
                0x2000da10       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000da10                tensor_conv2d_2_kernel_0
 .rodata.tensor_conv2d_2_kernel_0_data
                0x2000da28     0x1200 ./galaxy_sdk/speaker_inference.o
                0x2000da28                tensor_conv2d_2_kernel_0_data
 .rodata.max_pooling_1_config
                0x2000ec28       0x10 ./galaxy_sdk/speaker_inference.o
                0x2000ec28                max_pooling_1_config
 .rodata.conv2d_1_config
                0x2000ec38       0x28 ./galaxy_sdk/speaker_inference.o
                0x2000ec38                conv2d_1_config
 .rodata.tensor_conv2d_1_bias_0
                0x2000ec60       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000ec60                tensor_conv2d_1_bias_0
 .rodata.tensor_conv2d_1_bias_0_data
                0x2000ec78       0x10 ./galaxy_sdk/speaker_inference.o
                0x2000ec78                tensor_conv2d_1_bias_0_data
 .rodata.tensor_conv2d_1_kernel_0
                0x2000ec88       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000ec88                tensor_conv2d_1_kernel_0
 .rodata.tensor_conv2d_1_kernel_0_data
                0x2000eca0      0x190 ./galaxy_sdk/speaker_inference.o
                0x2000eca0                tensor_conv2d_1_kernel_0_data
 .rodata.tensor_input
                0x2000ee30       0x18 ./galaxy_sdk/speaker_inference.o
                0x2000ee30                tensor_input
 .rodata.vApplicationStackOverflowHook.str1.4
                0x2000ee48       0x1d D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 *fill*         0x2000ee65        0x3 
 .rodata.vApplicationMallocFailedHook.str1.4
                0x2000ee68        0xe D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 *fill*         0x2000ee76        0x2 
 .rodata.pdm_hw_cfg
                0x2000ee78       0x18 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .rodata.system_default_exception_handler.str1.4
                0x2000ee90       0x71 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 *fill*         0x2000ef01        0x3 
 .rodata.pdm_init.str1.4
                0x2000ef04       0x2e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 *fill*         0x2000ef32        0x2 
 .rodata.pdm_stop.str1.4
                0x2000ef34       0x4c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .rodata.pdm_start.str1.4
                0x2000ef80       0xad D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 *fill*         0x2000f02d        0x3 
 .rodata.pdm_ops
                0x2000f030       0x18 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .rodata.pdm_simu_data
                0x2000f048   0x1d7400 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .rodata.uart_printf.str1.4
                0x201e6448       0xad D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
                                  0x2 (size before relaxing)
 .rodata.vpi_timer_create.str1.4
                0x201e6448       0x2b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 *fill*         0x201e6473        0x1 
 .rodata.vpi_timer_start.str1.4
                0x201e6474       0x11 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 *fill*         0x201e6485        0x3 
 .rodata.vpi_timer_stop.str1.4
                0x201e6488       0x10 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .rodata.vpi_timer_delete.str1.4
                0x201e6498        0xf D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 *fill*         0x201e64a7        0x1 
 .rodata.CSWTCH.14
                0x201e64a8       0x48 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .rodata.osal_create_task.str1.4
                0x201e64f0       0x22 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 *fill*         0x201e6512        0x2 
 .rodata.vTaskStartScheduler.str1.4
                0x201e6514        0x9 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 *fill*         0x201e651d        0x3 
 .rodata.xTimerCreateTimerTask.str1.4
                0x201e6520        0xa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 *fill*         0x201e652a        0x2 
 .rodata.prvTimerTask
                0x201e652c       0x28 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .rodata._vfprintf_r.str1.4
                0x201e6554       0x13 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
 *fill*         0x201e6567        0x1 
 .rodata._printf_i.str1.4
                0x201e6568       0x25 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
 *fill*         0x201e658d        0x3 
 .rodata._printf_i
                0x201e6590       0x58 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
 .rodata.init_semihosting.str1.4
                0x201e65e8        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
 *(.gnu.linkonce.r.*)
                0x201e65ec                        . = ALIGN (0x4)
                0x201e65ec                        __rt_init_start = .
 *(SORT_BY_NAME(.rti_fn*))
                0x201e65ec                        __rt_init_end = .
                0x201e65ec                        . = ALIGN (0x4)
                0x201e65ec                        __fsymtab_start = .
 *(FSymTab)
                0x201e65ec                        __fsymtab_end = .
                0x201e65ec                        . = ALIGN (0x4)
                0x201e65ec                        __vsymtab_start = .
 *(VSymTab)
                0x201e65ec                        __vsymtab_end = .
                0x201e65ec                        . = ALIGN (0x4)
 *libble*.a:*(.text*)
                0x201e65ec                        __init_start = .
                0x201e65ec                        __init_PRE_KERNEL_1_start = .
 *(SORT_BY_NAME(.z_init_PRE_KERNEL_1[0-9]_*))
 *(SORT_BY_NAME(.z_init_PRE_KERNEL_1[1-9][0-9]_*))
                0x201e65ec                        __init_PRE_KERNEL_2_start = .
 *(SORT_BY_NAME(.z_init_PRE_KERNEL_2[0-9]_*))
 *(SORT_BY_NAME(.z_init_PRE_KERNEL_2[1-9][0-9]_*))
                0x201e65ec                        __init_POST_KERNEL_start = .
 *(SORT_BY_NAME(.z_init_POST_KERNEL[0-9]_*))
 *(SORT_BY_NAME(.z_init_POST_KERNEL[1-9][0-9]_*))
                0x201e65ec                        __init_APPLICATION_start = .
 *(SORT_BY_NAME(.z_init_APPLICATION[0-9]_*))
 *(SORT_BY_NAME(.z_init_APPLICATION[1-9][0-9]_*))
                0x201e65ec                        __init_end = .

.nonxip_text    0x201e65ec       0x30
 .nonxip_text   0x201e65ec        0xe ./galaxy_sdk/bsp/src/qemu_board.o
 .nonxip_text   0x201e65fa       0x22 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
                0x201e65fa                cpu_sleep

.rela.dyn       0x201e661c        0x0
 .rela.text.irq
                0x201e661c        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .rela.init     0x201e661c        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .rela.text.__libc_init_array
                0x201e661c        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .rela.text.__libc_fini_array
                0x201e661c        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .rela.text.__register_exitproc
                0x201e661c        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .rela.text._vfprintf_r
                0x201e661c        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .rela.text._sbrk
                0x201e661c        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o

.fini
 *(SORT_NONE(.fini))

.preinit_array  0x201e661c        0x0
                0x201e661c                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array)
                0x201e661c                        PROVIDE (__preinit_array_end = .)

.init_array     0x201e661c        0x4
                0x201e661c                        PROVIDE (__init_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.init_array.*) SORT_BY_INIT_PRIORITY(.ctors.*))
 *(.init_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .ctors)
 .init_array    0x201e661c        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
                0x201e6620                        PROVIDE (__init_array_end = .)

.fini_array     0x201e6620        0x0
                0x201e6620                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.fini_array.*) SORT_BY_INIT_PRIORITY(.dtors.*))
 *(.fini_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .dtors)
                0x201e6620                        PROVIDE (__fini_array_end = .)

.ctors
 *crtbegin.o(.ctors)
 *crtbegin?.o(.ctors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)

.dtors
 *crtbegin.o(.dtors)
 *crtbegin?.o(.dtors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)
                [!provide]                        PROVIDE (_ilm_lma = LOADADDR (.text))
                [!provide]                        PROVIDE (_ilm = ADDR (.text))
                [!provide]                        PROVIDE (_eilm = .)
                0x20000340                        PROVIDE (_text_lma = LOADADDR (.text))
                0x20000340                        PROVIDE (_text = ADDR (.text))
                0x201e6620                        PROVIDE (_etext = .)
                [!provide]                        PROVIDE (__etext = .)
                [!provide]                        PROVIDE (etext = .)

.data           0x90000000       0x90 load address 0x201e6620
 *(.data.ctest*)
 *(.data .data.*)
 .data.pdm_data
                0x90000000        0xc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .data.pdm_fifo
                0x9000000c       0x10 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .data._impure_data
                0x9000001c       0x4c D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-impure.o)
                0x9000001c                _impure_data
 .data.__sglue  0x90000068        0xc D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
                0x90000068                __sglue
 *(.gnu.linkonce.d.*)
                0x90000074                        . = ALIGN (0x4)
                0x90000074                        _net_buf_pool_list = .
                0x90000074                        _net_buf_pool_list_start = .
 *(._net_buf_pool.*)
                0x90000074                        _net_buf_pool_list_end = .
                0x90000074                        . = ALIGN (0x4)
                0x90000074                        _k_queue_list_start = .
 *(._k_queue.static.*)
                0x90000074                        _k_queue_list_end = .
                0x90000074                        . = ALIGN (0x4)
                0x90000074                        _k_mem_slab_list_start = .
 *(._k_mem_slab.static.*)
                0x90000074                        _k_mem_slab_list_end = .
                0x90000074                        . = ALIGN (0x4)
                0x90000074                        _bt_gatt_service_static_list_start = .
 *(._bt_gatt_service_static.static.*)
                0x90000074                        _bt_gatt_service_static_list_end = .
                0x90000074                        . = ALIGN (0x4)
                0x90000074                        _bt_l2cap_fixed_chan_list_start = .
 *(._bt_l2cap_fixed_chan.static.*)
                0x90000074                        _bt_l2cap_fixed_chan_list_end = .
                0x90000074                        . = ALIGN (0x4)
                0x90000074                        _settings_handler_static_list_start = .
 *(._settings_handler_static.static.*)
                0x90000074                        _settings_handler_static_list_end = .
                0x90000074                        . = ALIGN (0x4)
                0x90000074                        _k_sem_list_start = .
 *(._k_sem.static.*)
                0x90000074                        _k_sem_list_end = .
                0x90000074                        . = ALIGN (0x4)
                0x90000074                        _k_mutex_list_start = .
 *(._k_mutex.static.*)
                0x90000074                        _k_mutex_list_end = .
                0x90000074                        . = ALIGN (0x4)
                0x90000074                        _bt_conn_cb_list_start = .
 *(._bt_conn_cb.static.*)
                0x90000074                        _bt_conn_cb_list_end = .
                0x90000078                        . = ALIGN (0x8)
 *fill*         0x90000074        0x4 
                0x90000878                        PROVIDE (__global_pointer$ = (. + 0x800))
 *(.sdata .sdata.* .sdata*)
 .sdata.uxMaxSysCallMTH
                0x90000078        0x1 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x90000078                uxMaxSysCallMTH
 *fill*         0x90000079        0x3 
 .sdata.uxCriticalNesting
                0x9000007c        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .sdata.vs_logging_level
                0x90000080        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
                0x90000080                vs_logging_level
 .sdata._impure_ptr
                0x90000084        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-impure.o)
                0x90000084                _impure_ptr
 .sdata.__atexit_recursive_mutex
                0x90000088        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__call_atexit.o)
                0x90000088                __atexit_recursive_mutex
 *(.gnu.linkonce.s.*)
                0x90000090                        . = ALIGN (0x8)
 *fill*         0x9000008c        0x4 
                0x90000090                        __ipc_mem_start = .
                0x90000090                        . = ALIGN (0x8)

.RTOS_HEAP_SEC  0x90000090     0xdce8 load address 0x201e66b0
 .RTOS_HEAP_SEC
                0x90000090     0xdce8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                0x90000978                ucHeap

.tdata          0x9000dd78        0x0 load address 0x201f4398
                0x9000dd78                        PROVIDE (__tls_base = .)
 *(.tdata .tdata.* .gnu.linkonce.td.*)
                0x201e6620                        PROVIDE (_data_lma = LOADADDR (.data))
                0x90000000                        PROVIDE (_data = ADDR (.data))
                0x9000dd78                        PROVIDE (_edata = .)
                [!provide]                        PROVIDE (edata = .)
                [!provide]                        PROVIDE (_fbss = .)
                0x9000dd78                        PROVIDE (__bss_start = .)

.tbss           0x9000dd78        0x0
 *(.tbss .tbss.* .gnu.linkonce.tb.*)
 *(.tcommon)
                [!provide]                        PROVIDE (__tls_end = .)

.tbss_space     0x9000dd78        0x0
                0x9000dd78                        . = (. + SIZEOF (.tbss))

.bss            0x9000dd78     0x1908
 *(.sbss*)
 .sbss.nnom_memory_taken
                0x9000dd78        0x4 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                0x9000dd78                nnom_memory_taken
 .sbss.g_pdm_dev
                0x9000dd7c        0x4 ./galaxy_sdk/drivers/src/hal_pdm.o
 .sbss.model    0x9000dd80        0x4 ./galaxy_sdk/speaker_inference.o
 .sbss.ucMaxSysCallPriority
                0x9000dd84        0x1 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 *fill*         0x9000dd85        0x3 
 .sbss.board_ops
                0x9000dd88        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .sbss.clk_count
                0x9000dd8c        0x1 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 *fill*         0x9000dd8d        0x3 
 .sbss.clks     0x9000dd90        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .sbss.os_scheduler_start
                0x9000dd94        0x1 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
                0x9000dd94                os_scheduler_start
 *fill*         0x9000dd95        0x3 
 .sbss.ulTotalRunTime
                0x9000dd98        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.ulTaskSwitchedInTime
                0x9000dd9c        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.uxSchedulerSuspended
                0x9000dda0        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.xIdleTaskHandle
                0x9000dda4        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.xNextTaskUnblockTime
                0x9000dda8        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.uxTaskNumber
                0x9000ddac        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.xNumOfOverflows
                0x9000ddb0        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.xYieldPending
                0x9000ddb4        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.xPendedTicks
                0x9000ddb8        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.xSchedulerRunning
                0x9000ddbc        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.uxTopReadyPriority
                0x9000ddc0        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.xTickCount
                0x9000ddc4        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.uxCurrentNumberOfTasks
                0x9000ddc8        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.uxDeletedTasksWaitingCleanUp
                0x9000ddcc        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.pxOverflowDelayedTaskList
                0x9000ddd0        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.pxDelayedTaskList
                0x9000ddd4        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .sbss.pxCurrentTCB
                0x9000ddd8        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                0x9000ddd8                pxCurrentTCB
 .sbss.xLastTime.2
                0x9000dddc        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .sbss.xTimerTaskHandle
                0x9000dde0        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .sbss.xTimerQueue
                0x9000dde4        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .sbss.pxOverflowTimerList
                0x9000dde8        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .sbss.pxCurrentTimerList
                0x9000ddec        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .sbss.xBlockAllocatedBit
                0x9000ddf0        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .sbss.xNumberOfSuccessfulFrees
                0x9000ddf4        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .sbss.xNumberOfSuccessfulAllocations
                0x9000ddf8        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .sbss.xMinimumEverFreeBytesRemaining
                0x9000ddfc        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .sbss.xFreeBytesRemaining
                0x9000de00        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .sbss.pxEnd    0x9000de04        0x4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .sbss.xStart   0x9000de08        0x8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .sbss.__malloc_sbrk_start
                0x9000de10        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
                0x9000de10                __malloc_sbrk_start
 .sbss.__malloc_free_list
                0x9000de14        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
                0x9000de14                __malloc_free_list
 .sbss.__lock___malloc_recursive_mutex
                0x9000de18        0x1 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
                0x9000de18                __lock___malloc_recursive_mutex
 *fill*         0x9000de19        0x3 
 .sbss.__lock___atexit_recursive_mutex
                0x9000de1c        0x1 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
                0x9000de1c                __lock___atexit_recursive_mutex
 *fill*         0x9000de1d        0x3 
 .sbss.__lock___sfp_recursive_mutex
                0x9000de20        0x1 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
                0x9000de20                __lock___sfp_recursive_mutex
 *fill*         0x9000de21        0x3 
 .sbss.__atexit
                0x9000de24        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__call_atexit.o)
                0x9000de24                __atexit
 .sbss.__stdio_exit_handler
                0x9000de28        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
                0x9000de28                __stdio_exit_handler
 .sbss.errno    0x9000de2c        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reent.o)
                0x9000de2c                errno
 .sbss.heap_ptr
                0x9000de30        0x4 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_sbrk.o)
                0x9000de30                heap_ptr
 *(.gnu.linkonce.sb.*)
 *(.bss .bss.*)
 .bss.model.0   0x9000de34       0x88 ./galaxy_sdk/speaker_inference.o
 .bss.output_buffer
                0x9000debc       0x10 ./galaxy_sdk/speaker_inference.o
 .bss.input_buffer
                0x9000decc      0x948 ./galaxy_sdk/speaker_inference.o
 .bss.nnom_output_data
                0x9000e814       0x10 ./galaxy_sdk/speaker_inference.o
 .bss.nnom_input_data
                0x9000e824      0x948 ./galaxy_sdk/speaker_inference.o
 .bss.system_exception_handlers
                0x9000f16c       0x34 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .bss.pdm_ctx   0x9000f1a0       0x2c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .bss.xSuspendedTaskList
                0x9000f1cc       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .bss.xTasksWaitingTermination
                0x9000f1e0       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .bss.xPendingReadyList
                0x9000f1f4       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .bss.xDelayedTaskList2
                0x9000f208       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .bss.xDelayedTaskList1
                0x9000f21c       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .bss.pxReadyTasksLists
                0x9000f230       0xa0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .bss.xStaticTimerQueue.0
                0x9000f2d0       0x54 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .bss.ucStaticTimerQueueStorage.1
                0x9000f324       0xf0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .bss.xActiveTimerList2
                0x9000f414       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .bss.xActiveTimerList1
                0x9000f428       0x14 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .bss.__atexit0
                0x9000f43c       0x8c D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o)
                0x9000f43c                __atexit0
 .bss.__sf      0x9000f4c8      0x138 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
                0x9000f4c8                __sf
 .bss.fdtable   0x9000f600       0x80 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
 *(.gnu.linkonce.b.*)
 *(COMMON)
                0x9000f680                        . = ALIGN (0x4)
                0x9000f680                        PROVIDE (_end = .)
                [!provide]                        PROVIDE (end = .)

.heap           0x9000f680     0x5000
                0x9000f680                        . = ALIGN (0x10)
                [!provide]                        PROVIDE (__heap_start = .)
                0x90014680                        . = (. + __HEAP_SIZE)
 *fill*         0x9000f680     0x5000 
                0x90014680                        . = ALIGN (0x10)
                [!provide]                        PROVIDE (__heap_limit = .)

.stack          0x9003f800      0x800 load address 0x90014680
                0x9003f800                        . = ALIGN (0x10)
                [!provide]                        PROVIDE (_heap_end = .)
                [!provide]                        PROVIDE (__heap_end = .)
                [!provide]                        PROVIDE (__StackLimit = .)
                [!provide]                        PROVIDE (__StackBottom = .)
                0x90040000                        . = (. + __TOT_STACK_SIZE)
 *fill*         0x9003f800      0x800 
                0x90040000                        . = ALIGN (0x10)
                [!provide]                        PROVIDE (__StackTop = .)
                0x90040000                        PROVIDE (_sp = .)
OUTPUT(qemu.out elf32-littleriscv)

.debug_info     0x00000000    0x30a0d
 .debug_info    0x00000000     0x1a83 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .debug_info    0x00001a83     0x1126 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .debug_info    0x00002ba9      0xf6c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .debug_info    0x00003b15     0x173f ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .debug_info    0x00005254     0x1377 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .debug_info    0x000065cb      0xede ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .debug_info    0x000074a9     0x1102 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .debug_info    0x000085ab     0x1116 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .debug_info    0x000096c1      0xef9 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .debug_info    0x0000a5ba      0xed6 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .debug_info    0x0000b490     0x2bf7 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .debug_info    0x0000e087      0xf6f ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .debug_info    0x0000eff6      0xf5d ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .debug_info    0x0000ff53     0x38d9 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .debug_info    0x0001382c     0x374f ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .debug_info    0x00016f7b      0x7a9 ./galaxy_sdk/drivers/src/hal_pdm.o
 .debug_info    0x00017724       0x23 ./galaxy_sdk/bsp/src/portasm.o
 .debug_info    0x00017747      0x303 ./galaxy_sdk/bsp/src/qemu_board.o
 .debug_info    0x00017a4a       0x25 ./galaxy_sdk/bsp/src/startup_riscv.o
 .debug_info    0x00017a6f      0xd58 ./galaxy_sdk/main.o
 .debug_info    0x000187c7     0x22f2 ./galaxy_sdk/speaker_inference.o
 .debug_info    0x0001aab9     0x296a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .debug_info    0x0001d423      0x4cc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .debug_info    0x0001d8ef     0x23bf D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .debug_info    0x0001fcae      0x6cc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .debug_info    0x0002037a      0x309 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .debug_info    0x00020683      0x5db D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .debug_info    0x00020c5e      0xff8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .debug_info    0x00021c56      0xd83 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .debug_info    0x000229d9      0xde8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .debug_info    0x000237c1      0x712 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .debug_info    0x00023ed3      0x35e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .debug_info    0x00024231       0xb6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .debug_info    0x000242e7      0x51c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .debug_info    0x00024803      0x230 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .debug_info    0x00024a33      0xa33 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .debug_info    0x00025466      0x681 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .debug_info    0x00025ae7      0x190 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .debug_info    0x00025c77     0x4ad2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .debug_info    0x0002a749      0x2a7 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .debug_info    0x0002a9f0     0x200b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .debug_info    0x0002c9fb      0x6d6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .debug_info    0x0002d0d1     0x393c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)

.debug_abbrev   0x00000000     0x7391
 .debug_abbrev  0x00000000      0x3ef ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .debug_abbrev  0x000003ef      0x28d ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .debug_abbrev  0x0000067c      0x20a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .debug_abbrev  0x00000886      0x341 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .debug_abbrev  0x00000bc7      0x2d3 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .debug_abbrev  0x00000e9a      0x205 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .debug_abbrev  0x0000109f      0x1fc ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .debug_abbrev  0x0000129b      0x20b ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .debug_abbrev  0x000014a6      0x1f7 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .debug_abbrev  0x0000169d      0x209 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .debug_abbrev  0x000018a6      0x5c8 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .debug_abbrev  0x00001e6e      0x29c ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .debug_abbrev  0x0000210a      0x2a9 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .debug_abbrev  0x000023b3      0x2e8 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .debug_abbrev  0x0000269b      0x323 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .debug_abbrev  0x000029be      0x1cf ./galaxy_sdk/drivers/src/hal_pdm.o
 .debug_abbrev  0x00002b8d       0x12 ./galaxy_sdk/bsp/src/portasm.o
 .debug_abbrev  0x00002b9f      0x163 ./galaxy_sdk/bsp/src/qemu_board.o
 .debug_abbrev  0x00002d02       0x14 ./galaxy_sdk/bsp/src/startup_riscv.o
 .debug_abbrev  0x00002d16      0x279 ./galaxy_sdk/main.o
 .debug_abbrev  0x00002f8f      0x40d ./galaxy_sdk/speaker_inference.o
 .debug_abbrev  0x0000339c      0x5f6 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .debug_abbrev  0x00003992      0x127 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .debug_abbrev  0x00003ab9      0x5e5 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .debug_abbrev  0x0000409e      0x3b7 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .debug_abbrev  0x00004455      0x154 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .debug_abbrev  0x000045a9      0x2d1 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .debug_abbrev  0x0000487a      0x3a8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .debug_abbrev  0x00004c22      0x372 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .debug_abbrev  0x00004f94      0x326 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .debug_abbrev  0x000052ba      0x15f D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .debug_abbrev  0x00005419      0x114 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .debug_abbrev  0x0000552d       0x76 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .debug_abbrev  0x000055a3      0x210 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .debug_abbrev  0x000057b3      0x197 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .debug_abbrev  0x0000594a      0x2c3 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .debug_abbrev  0x00005c0d      0x25b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .debug_abbrev  0x00005e68      0x13b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .debug_abbrev  0x00005fa3      0x602 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .debug_abbrev  0x000065a5      0x116 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .debug_abbrev  0x000066bb      0x4aa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .debug_abbrev  0x00006b65      0x319 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .debug_abbrev  0x00006e7e      0x513 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)

.debug_loclists
                0x00000000    0x1eb63
 .debug_loclists
                0x00000000      0x9ce ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .debug_loclists
                0x000009ce      0x14f ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .debug_loclists
                0x00000b1d      0x108 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .debug_loclists
                0x00000c25      0x60b ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .debug_loclists
                0x00001230      0x32b ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .debug_loclists
                0x0000155b       0xe7 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .debug_loclists
                0x00001642      0x1f7 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .debug_loclists
                0x00001839      0x290 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .debug_loclists
                0x00001ac9       0xdb ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .debug_loclists
                0x00001ba4       0xf2 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .debug_loclists
                0x00001c96     0x1697 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .debug_loclists
                0x0000332d      0x1ec ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .debug_loclists
                0x00003519      0x8ee ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .debug_loclists
                0x00003e07     0x66bf ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .debug_loclists
                0x0000a4c6     0x679e ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .debug_loclists
                0x00010c64      0x2dc ./galaxy_sdk/drivers/src/hal_pdm.o
 .debug_loclists
                0x00010f40       0x4c ./galaxy_sdk/bsp/src/qemu_board.o
 .debug_loclists
                0x00010f8c      0x11f ./galaxy_sdk/main.o
 .debug_loclists
                0x000110ab      0x775 ./galaxy_sdk/speaker_inference.o
 .debug_loclists
                0x00011820     0x12cc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .debug_loclists
                0x00012aec      0xc36 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .debug_loclists
                0x00013722      0x230 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .debug_loclists
                0x00013952      0x139 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .debug_loclists
                0x00013a8b       0xd7 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .debug_loclists
                0x00013b62      0xbc4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .debug_loclists
                0x00014726      0x88a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .debug_loclists
                0x00014fb0     0x190d D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .debug_loclists
                0x000168bd      0x45d D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .debug_loclists
                0x00016d1a      0x24b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .debug_loclists
                0x00016f65      0x1af D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .debug_loclists
                0x00017114       0x4c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .debug_loclists
                0x00017160      0x4fa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .debug_loclists
                0x0001765a      0x15f D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .debug_loclists
                0x000177b9       0x4c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .debug_loclists
                0x00017805     0x32d8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .debug_loclists
                0x0001aadd       0x85 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .debug_loclists
                0x0001ab62     0x1394 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .debug_loclists
                0x0001bef6      0x3b1 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .debug_loclists
                0x0001c2a7     0x28bc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)

.debug_aranges  0x00000000     0x1378
 .debug_aranges
                0x00000000       0xe0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .debug_aranges
                0x000000e0       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .debug_aranges
                0x00000118       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .debug_aranges
                0x00000150       0x48 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .debug_aranges
                0x00000198       0x40 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .debug_aranges
                0x000001d8       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .debug_aranges
                0x00000210       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .debug_aranges
                0x00000248       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .debug_aranges
                0x00000280       0x30 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .debug_aranges
                0x000002b0       0x38 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .debug_aranges
                0x000002e8       0xf0 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .debug_aranges
                0x000003d8       0x58 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .debug_aranges
                0x00000430       0x78 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .debug_aranges
                0x000004a8      0x150 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .debug_aranges
                0x000005f8      0x148 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .debug_aranges
                0x00000740       0x60 ./galaxy_sdk/drivers/src/hal_pdm.o
 .debug_aranges
                0x000007a0       0x28 ./galaxy_sdk/bsp/src/portasm.o
 .debug_aranges
                0x000007c8       0x30 ./galaxy_sdk/bsp/src/qemu_board.o
 .debug_aranges
                0x000007f8       0x20 ./galaxy_sdk/bsp/src/startup_riscv.o
 .debug_aranges
                0x00000818       0x30 ./galaxy_sdk/main.o
 .debug_aranges
                0x00000848       0x60 ./galaxy_sdk/speaker_inference.o
 .debug_aranges
                0x000008a8       0xc8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .debug_aranges
                0x00000970       0x20 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .debug_aranges
                0x00000990      0x120 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .debug_aranges
                0x00000ab0       0x58 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .debug_aranges
                0x00000b08       0x38 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .debug_aranges
                0x00000b40       0x50 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .debug_aranges
                0x00000b90       0x70 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .debug_aranges
                0x00000c00       0x50 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .debug_aranges
                0x00000c50       0x48 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .debug_aranges
                0x00000c98       0x60 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .debug_aranges
                0x00000cf8       0x30 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .debug_aranges
                0x00000d28       0x20 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .debug_aranges
                0x00000d48       0x88 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .debug_aranges
                0x00000dd0       0x50 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .debug_aranges
                0x00000e20       0x60 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .debug_aranges
                0x00000e80       0x38 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .debug_aranges
                0x00000eb8       0x38 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .debug_aranges
                0x00000ef0      0x220 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .debug_aranges
                0x00001110       0x40 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .debug_aranges
                0x00001150       0xb8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .debug_aranges
                0x00001208       0x50 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .debug_aranges
                0x00001258      0x120 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)

.debug_rnglists
                0x00000000     0x4701
 .debug_rnglists
                0x00000000      0x1e3 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .debug_rnglists
                0x000001e3       0x56 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .debug_rnglists
                0x00000239       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .debug_rnglists
                0x0000026a       0xbc ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .debug_rnglists
                0x00000326       0x72 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .debug_rnglists
                0x00000398       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .debug_rnglists
                0x000003c9       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .debug_rnglists
                0x000003fa       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .debug_rnglists
                0x0000042b       0x28 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .debug_rnglists
                0x00000453       0x31 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .debug_rnglists
                0x00000484      0x392 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .debug_rnglists
                0x00000816       0x68 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .debug_rnglists
                0x0000087e      0x110 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .debug_rnglists
                0x0000098e      0x8a9 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .debug_rnglists
                0x00001237      0xa06 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .debug_rnglists
                0x00001c3d       0x5e ./galaxy_sdk/drivers/src/hal_pdm.o
 .debug_rnglists
                0x00001c9b       0x1b ./galaxy_sdk/bsp/src/portasm.o
 .debug_rnglists
                0x00001cb6       0x28 ./galaxy_sdk/bsp/src/qemu_board.o
 .debug_rnglists
                0x00001cde       0x5f ./galaxy_sdk/main.o
 .debug_rnglists
                0x00001d3d      0x123 ./galaxy_sdk/speaker_inference.o
 .debug_rnglists
                0x00001e60      0x6dd D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .debug_rnglists
                0x0000253d       0x16 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .debug_rnglists
                0x00002553      0x38a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .debug_rnglists
                0x000028dd       0x8e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .debug_rnglists
                0x0000296b       0x31 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .debug_rnglists
                0x0000299c       0xaa D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .debug_rnglists
                0x00002a46      0x1f8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .debug_rnglists
                0x00002c3e       0xfb D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .debug_rnglists
                0x00002d39      0x17e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .debug_rnglists
                0x00002eb7       0x5e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .debug_rnglists
                0x00002f15       0x28 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .debug_rnglists
                0x00002f3d       0x16 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .debug_rnglists
                0x00002f53       0x8b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .debug_rnglists
                0x00002fde       0x5f D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .debug_rnglists
                0x0000303d      0x109 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .debug_rnglists
                0x00003146       0x69 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .debug_rnglists
                0x000031af       0x31 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .debug_rnglists
                0x000031e0      0xa4a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .debug_rnglists
                0x00003c2a       0x3a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .debug_rnglists
                0x00003c64      0x2cb D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .debug_rnglists
                0x00003f2f       0x5f D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .debug_rnglists
                0x00003f8e      0x773 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)

.debug_line     0x00000000    0x2d74e
 .debug_line    0x00000000     0x1450 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .debug_line    0x00001450      0x3fc ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .debug_line    0x0000184c      0x3b3 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .debug_line    0x00001bff      0xe04 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .debug_line    0x00002a03      0x9fa ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .debug_line    0x000033fd      0x33d ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .debug_line    0x0000373a      0x5dd ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .debug_line    0x00003d17      0x6a5 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .debug_line    0x000043bc      0x27a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .debug_line    0x00004636      0x3ae ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .debug_line    0x000049e4     0x2a3d ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .debug_line    0x00007421      0x302 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .debug_line    0x00007723      0xcc5 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .debug_line    0x000083e8     0x582a ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .debug_line    0x0000dc12     0x5ab6 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .debug_line    0x000136c8      0x477 ./galaxy_sdk/drivers/src/hal_pdm.o
 .debug_line    0x00013b3f      0x356 ./galaxy_sdk/bsp/src/portasm.o
 .debug_line    0x00013e95       0xef ./galaxy_sdk/bsp/src/qemu_board.o
 .debug_line    0x00013f84      0x1c5 ./galaxy_sdk/bsp/src/startup_riscv.o
 .debug_line    0x00014149      0x343 ./galaxy_sdk/main.o
 .debug_line    0x0001448c      0xd74 ./galaxy_sdk/speaker_inference.o
 .debug_line    0x00015200     0x2392 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .debug_line    0x00017592       0x9f D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .debug_line    0x00017631     0x208f D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .debug_line    0x000196c0      0x4b0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .debug_line    0x00019b70      0x1d7 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .debug_line    0x00019d47      0x402 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .debug_line    0x0001a149      0xe80 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .debug_line    0x0001afc9      0xa1a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .debug_line    0x0001b9e3     0x11c5 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .debug_line    0x0001cba8      0x4c9 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .debug_line    0x0001d071      0x228 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .debug_line    0x0001d299       0x6a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .debug_line    0x0001d303      0x2f0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .debug_line    0x0001d5f3      0x203 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .debug_line    0x0001d7f6      0x8c4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .debug_line    0x0001e0ba      0x4a7 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .debug_line    0x0001e561       0xbd D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .debug_line    0x0001e61e     0x7534 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .debug_line    0x00025b52      0x437 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .debug_line    0x00025f89     0x1c2b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .debug_line    0x00027bb4      0xc05 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .debug_line    0x000287b9     0x4f95 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)

.debug_str      0x00000000     0x7546
 .debug_str     0x00000000      0x8bb ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
                                0xa87 (size before relaxing)
 .debug_str     0x000008bb      0x11c ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
                                0x88d (size before relaxing)
 .debug_str     0x000009d7       0x95 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
                                0x81c (size before relaxing)
 .debug_str     0x00000a6c      0x17e ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
                                0x9ed (size before relaxing)
 .debug_str     0x00000bea       0x91 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
                                0x8d5 (size before relaxing)
 .debug_str     0x00000c7b       0x43 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
                                0x7ca (size before relaxing)
 .debug_str     0x00000cbe       0x5a ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
                                0x839 (size before relaxing)
 .debug_str     0x00000d18       0x2b ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
                                0x8a8 (size before relaxing)
 .debug_str     0x00000d43       0x28 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
                                0x7f8 (size before relaxing)
 .debug_str     0x00000d6b       0x54 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
                                0x7db (size before relaxing)
 .debug_str     0x00000dbf      0x548 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                                0xd59 (size before relaxing)
 .debug_str     0x00001307       0x4b ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
                                0x7c7 (size before relaxing)
 .debug_str     0x00001352       0x3e ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
                                0x427 (size before relaxing)
 .debug_str     0x00001390      0x5f1 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
                                0xa3d (size before relaxing)
 .debug_str     0x00001981      0x309 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
                                0xaac (size before relaxing)
 .debug_str     0x00001c8a      0x407 ./galaxy_sdk/drivers/src/hal_pdm.o
                                0x60a (size before relaxing)
 .debug_str     0x00002091       0xa6 ./galaxy_sdk/bsp/src/portasm.o
 .debug_str     0x00002137      0x163 ./galaxy_sdk/bsp/src/qemu_board.o
                                0x451 (size before relaxing)
 .debug_str     0x0000229a       0x5a ./galaxy_sdk/bsp/src/startup_riscv.o
                                 0xac (size before relaxing)
 .debug_str     0x000022f4      0x280 ./galaxy_sdk/main.o
                                0x517 (size before relaxing)
 .debug_str     0x00002574      0x823 ./galaxy_sdk/speaker_inference.o
                               0x126e (size before relaxing)
 .debug_str     0x00002d97      0x990 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                                0xafb (size before relaxing)
 .debug_str     0x00003727      0x3f9 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
                                0x842 (size before relaxing)
 .debug_str     0x00003b20      0xcad D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
                               0x157d (size before relaxing)
 .debug_str     0x000047cd      0x202 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
                                0x444 (size before relaxing)
 .debug_str     0x000049cf       0x22 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
                                0x39f (size before relaxing)
 .debug_str     0x000049f1       0xd4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
                                0x46a (size before relaxing)
 .debug_str     0x00004ac5      0x2fd D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
                                0x9a1 (size before relaxing)
 .debug_str     0x00004dc2      0x1c2 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
                                0x94a (size before relaxing)
 .debug_str     0x00004f84      0x21a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
                                0x466 (size before relaxing)
 .debug_str     0x0000519e      0x162 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
                                0x4d2 (size before relaxing)
 .debug_str     0x00005300       0x33 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
                                0x43d (size before relaxing)
 .debug_str     0x00005333       0x1c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
                                0x181 (size before relaxing)
 .debug_str     0x0000534f      0x16a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
                                0x41d (size before relaxing)
 .debug_str     0x000054b9       0xa4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
                                0x28c (size before relaxing)
 .debug_str     0x0000555d       0xe4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
                                0x45f (size before relaxing)
 .debug_str     0x00005641      0x1f4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
                                0x453 (size before relaxing)
 .debug_str     0x00005835       0x2d D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
                                0x1ba (size before relaxing)
 .debug_str     0x00005862      0xe54 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                               0x164a (size before relaxing)
 .debug_str     0x000066b6       0x41 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
                                0x2a7 (size before relaxing)
 .debug_str     0x000066f7      0x658 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
                                0xd13 (size before relaxing)
 .debug_str     0x00006d4f      0x261 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
                                0x542 (size before relaxing)
 .debug_str     0x00006fb0      0x596 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
                                0xdb5 (size before relaxing)

.debug_line_str
                0x00000000     0x2274
 .debug_line_str
                0x00000000      0x381 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
                                0x3e9 (size before relaxing)
 .debug_line_str
                0x00000381       0x90 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
                                0x38e (size before relaxing)
 .debug_line_str
                0x00000411       0x95 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
                                0x3fe (size before relaxing)
 .debug_line_str
                0x000004a6       0x7f ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
                                0x3f6 (size before relaxing)
 .debug_line_str
                0x00000525       0x7d ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
                                0x3f2 (size before relaxing)
 .debug_line_str
                0x000005a2       0x81 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
                                0x38d (size before relaxing)
 .debug_line_str
                0x00000623       0x70 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
                                0x3e0 (size before relaxing)
 .debug_line_str
                0x00000693       0x72 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
                                0x39f (size before relaxing)
 .debug_line_str
                0x00000705       0x71 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
                                0x3ec (size before relaxing)
 .debug_line_str
                0x00000776       0x81 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
                                0x38d (size before relaxing)
 .debug_line_str
                0x000007f7       0xe5 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
                                0x3d4 (size before relaxing)
 .debug_line_str
                0x000008dc       0x6f ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
                                0x2f8 (size before relaxing)
 .debug_line_str
                0x0000094b       0x6f ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
                                0x35c (size before relaxing)
 .debug_line_str
                0x000009ba       0xd7 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
                                0x358 (size before relaxing)
 .debug_line_str
                0x00000a91       0x76 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
                                0x364 (size before relaxing)
 .debug_line_str
                0x00000b07      0x11b ./galaxy_sdk/drivers/src/hal_pdm.o
                                0x26c (size before relaxing)
 .debug_line_str
                0x00000c22       0x54 ./galaxy_sdk/bsp/src/portasm.o
                                 0x98 (size before relaxing)
 .debug_line_str
                0x00000c76       0xbb ./galaxy_sdk/bsp/src/qemu_board.o
                                0x2c2 (size before relaxing)
 .debug_line_str
                0x00000d31       0x10 ./galaxy_sdk/bsp/src/startup_riscv.o
                                 0x9e (size before relaxing)
 .debug_line_str
                0x00000d41      0x175 ./galaxy_sdk/main.o
                                0x37f (size before relaxing)
 .debug_line_str
                0x00000eb6       0x87 ./galaxy_sdk/speaker_inference.o
                                0x446 (size before relaxing)
 .debug_line_str
                0x00000f3d      0x4bb D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
                                0x563 (size before relaxing)
 .debug_line_str
                0x000013f8      0x142 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
                                0x37d (size before relaxing)
 .debug_line_str
                0x0000153a      0x113 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
                                0x464 (size before relaxing)
 .debug_line_str
                0x0000164d       0xbd D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
                                0x42b (size before relaxing)
 .debug_line_str
                0x0000170a       0x7e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
                                0x298 (size before relaxing)
 .debug_line_str
                0x00001788       0xf4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
                                0x38f (size before relaxing)
 .debug_line_str
                0x0000187c      0x15a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
                                0x4ad (size before relaxing)
 .debug_line_str
                0x000019d6      0x107 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
                                0x4df (size before relaxing)
 .debug_line_str
                0x00001add      0x10c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
                                0x3f6 (size before relaxing)
 .debug_line_str
                0x00001be9       0xb0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
                                0x370 (size before relaxing)
 .debug_line_str
                0x00001c99       0x8a D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
                                0x264 (size before relaxing)
 .debug_line_str
                0x00001d23       0x4e D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
                                0x182 (size before relaxing)
 .debug_line_str
                0x00001d71       0xfe D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
                                0x3c7 (size before relaxing)
 .debug_line_str
                0x00001e6f       0x55 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
                                0x361 (size before relaxing)
 .debug_line_str
                0x00001ec4       0x7d D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
                                0x497 (size before relaxing)
 .debug_line_str
                0x00001f41       0x50 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
                                0x4d2 (size before relaxing)
 .debug_line_str
                0x00001f91       0x4b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
                                0x23d (size before relaxing)
 .debug_line_str
                0x00001fdc       0xf9 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
                                0x4c4 (size before relaxing)
 .debug_line_str
                0x000020d5       0x4b D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
                                0x309 (size before relaxing)
 .debug_line_str
                0x00002120       0x55 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
                                0x44f (size before relaxing)
 .debug_line_str
                0x00002175       0xb3 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
                                0x41a (size before relaxing)
 .debug_line_str
                0x00002228       0x4c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
                                0x4b0 (size before relaxing)

.comment        0x00000000       0x22
 .comment       0x00000000       0x22 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
                                 0x23 (size before relaxing)
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .comment       0x00000022       0x23 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .comment       0x00000022       0x23 ./galaxy_sdk/drivers/src/hal_pdm.o
 .comment       0x00000022       0x23 ./galaxy_sdk/bsp/src/qemu_board.o
 .comment       0x00000022       0x23 ./galaxy_sdk/main.o
 .comment       0x00000022       0x23 ./galaxy_sdk/speaker_inference.o
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .comment       0x00000022       0x23 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-atexit.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-calloc.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-callocr.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-malloc.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mlock.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-printf.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putchar.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-vprintf.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wbuf.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wsetup.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-impure.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sbrkr.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-init.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fini.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memcpy.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strlen.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__call_atexit.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-freer.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fwalk.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putc.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memchr.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-closer.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reent.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fstatr.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-isattyr.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lseekr.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-readr.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-writer.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_close.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fstat.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_isatty.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_lseek.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_open.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_read.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_sbrk.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_stat_common.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_write.o)
 .comment       0x00000022       0x23 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-errno.o)

.note.GNU-stack
                0x00000000        0x0
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/drivers/src/hal_pdm.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/bsp/src/qemu_board.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/main.o
 .note.GNU-stack
                0x00000000        0x0 ./galaxy_sdk/speaker_inference.o
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-atexit.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-calloc.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-callocr.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-malloc.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mlock.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-printf.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putchar.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-vprintf.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wbuf.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wsetup.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-impure.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sbrkr.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-init.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fini.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memcpy.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strlen.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__call_atexit.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-freer.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fwalk.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putc.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memchr.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-closer.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reent.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fstatr.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-isattyr.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lseekr.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-readr.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-writer.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_close.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fstat.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_isatty.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_lseek.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_open.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_read.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_sbrk.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_stat_common.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_write.o)
 .note.GNU-stack
                0x00000000        0x0 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-errno.o)

.riscv.attributes
                0x00000000       0x49
 .riscv.attributes
                0x00000000       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .riscv.attributes
                0x00000045       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .riscv.attributes
                0x0000008a       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .riscv.attributes
                0x000000cf       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .riscv.attributes
                0x00000114       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .riscv.attributes
                0x00000159       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .riscv.attributes
                0x0000019e       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .riscv.attributes
                0x000001e3       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .riscv.attributes
                0x00000228       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .riscv.attributes
                0x0000026d       0x45 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .riscv.attributes
                0x000002b2       0x45 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .riscv.attributes
                0x000002f7       0x45 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .riscv.attributes
                0x0000033c       0x45 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .riscv.attributes
                0x00000381       0x45 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .riscv.attributes
                0x000003c6       0x45 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .riscv.attributes
                0x0000040b       0x45 ./galaxy_sdk/drivers/src/hal_pdm.o
 .riscv.attributes
                0x00000450       0x45 ./galaxy_sdk/bsp/src/portasm.o
 .riscv.attributes
                0x00000495       0x45 ./galaxy_sdk/bsp/src/qemu_board.o
 .riscv.attributes
                0x000004da       0x45 ./galaxy_sdk/bsp/src/startup_riscv.o
 .riscv.attributes
                0x0000051f       0x45 ./galaxy_sdk/main.o
 .riscv.attributes
                0x00000564       0x45 ./galaxy_sdk/speaker_inference.o
 .riscv.attributes
                0x000005a9       0x49 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .riscv.attributes
                0x000005f2       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .riscv.attributes
                0x00000637       0x49 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .riscv.attributes
                0x00000680       0x49 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .riscv.attributes
                0x000006c9       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .riscv.attributes
                0x0000070e       0x49 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .riscv.attributes
                0x00000757       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .riscv.attributes
                0x0000079c       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .riscv.attributes
                0x000007e1       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .riscv.attributes
                0x00000826       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .riscv.attributes
                0x0000086b       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .riscv.attributes
                0x000008b0       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .riscv.attributes
                0x000008f5       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .riscv.attributes
                0x0000093a       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .riscv.attributes
                0x0000097f       0x49 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .riscv.attributes
                0x000009c8       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .riscv.attributes
                0x00000a0d       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .riscv.attributes
                0x00000a52       0x49 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .riscv.attributes
                0x00000a9b       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .riscv.attributes
                0x00000ae0       0x49 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .riscv.attributes
                0x00000b29       0x45 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .riscv.attributes
                0x00000b6e       0x49 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
 .riscv.attributes
                0x00000bb7       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-atexit.o)
 .riscv.attributes
                0x00000bfa       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-calloc.o)
 .riscv.attributes
                0x00000c3d       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-callocr.o)
 .riscv.attributes
                0x00000c80       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-malloc.o)
 .riscv.attributes
                0x00000cc3       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mallocr.o)
 .riscv.attributes
                0x00000d06       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-mlock.o)
 .riscv.attributes
                0x00000d49       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-printf.o)
 .riscv.attributes
                0x00000d8c       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putchar.o)
 .riscv.attributes
                0x00000dcf       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-puts.o)
 .riscv.attributes
                0x00000e12       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-vprintf.o)
 .riscv.attributes
                0x00000e55       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wbuf.o)
 .riscv.attributes
                0x00000e98       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-wsetup.o)
 .riscv.attributes
                0x00000edb       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-impure.o)
 .riscv.attributes
                0x00000f1e       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-sbrkr.o)
 .riscv.attributes
                0x00000f61       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-init.o)
 .riscv.attributes
                0x00000fa4       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fini.o)
 .riscv.attributes
                0x00000fe7       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lock.o)
 .riscv.attributes
                0x0000102a       0x41 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memset.o)
 .riscv.attributes
                0x0000106b       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memcpy.o)
 .riscv.attributes
                0x000010ae       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-strlen.o)
 .riscv.attributes
                0x000010f1       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__atexit.o)
 .riscv.attributes
                0x00001134       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-__call_atexit.o)
 .riscv.attributes
                0x00001177       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-freer.o)
 .riscv.attributes
                0x000011ba       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf.o)
 .riscv.attributes
                0x000011fd       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-nano-vfprintf_i.o)
 .riscv.attributes
                0x00001240       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fflush.o)
 .riscv.attributes
                0x00001283       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-findfp.o)
 .riscv.attributes
                0x000012c6       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fwalk.o)
 .riscv.attributes
                0x00001309       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-makebuf.o)
 .riscv.attributes
                0x0000134c       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-putc.o)
 .riscv.attributes
                0x0000138f       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-stdio.o)
 .riscv.attributes
                0x000013d2       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-memchr.o)
 .riscv.attributes
                0x00001415       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-closer.o)
 .riscv.attributes
                0x00001458       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-reent.o)
 .riscv.attributes
                0x0000149b       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-fstatr.o)
 .riscv.attributes
                0x000014de       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-isattyr.o)
 .riscv.attributes
                0x00001521       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-lseekr.o)
 .riscv.attributes
                0x00001564       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-readr.o)
 .riscv.attributes
                0x000015a7       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-writer.o)
 .riscv.attributes
                0x000015ea       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_close.o)
 .riscv.attributes
                0x0000162d       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fdtable.o)
 .riscv.attributes
                0x00001670       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_fstat.o)
 .riscv.attributes
                0x000016b3       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_isatty.o)
 .riscv.attributes
                0x000016f6       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_lseek.o)
 .riscv.attributes
                0x00001739       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_open.o)
 .riscv.attributes
                0x0000177c       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_read.o)
 .riscv.attributes
                0x000017bf       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_sbrk.o)
 .riscv.attributes
                0x00001802       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_stat_common.o)
 .riscv.attributes
                0x00001845       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libsemihost.a(riscv_libsemihost_a-semihost-sys_write.o)
 .riscv.attributes
                0x00001888       0x43 D:/NucleiStudio/NucleiStudio/toolchain/gcc/bin/../lib/gcc/riscv64-unknown-elf/13.1.1/../../../../riscv64-unknown-elf/lib/rv32imafc_xxldsp/ilp32f\libc_nano.a(libc_a-errno.o)

.debug_frame    0x00000000     0x4eb8
 .debug_frame   0x00000000      0x378 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o
 .debug_frame   0x00000378       0xa0 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o
 .debug_frame   0x00000418       0x98 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o
 .debug_frame   0x000004b0      0x124 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o
 .debug_frame   0x000005d4       0xec ./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o
 .debug_frame   0x000006c0       0x84 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o
 .debug_frame   0x00000744       0xbc ./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o
 .debug_frame   0x00000800       0xc4 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o
 .debug_frame   0x000008c4       0x78 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o
 .debug_frame   0x0000093c       0x98 ./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o
 .debug_frame   0x000009d4      0x420 ./galaxy_sdk/modules/external/nnom/src/core/nnom.o
 .debug_frame   0x00000df4       0xc0 ./galaxy_sdk/modules/external/nnom/src/core/nnom_layers.o
 .debug_frame   0x00000eb4      0x174 ./galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.o
 .debug_frame   0x00001028      0x8cc ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local.o
 .debug_frame   0x000018f4      0x9c0 ./galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.o
 .debug_frame   0x000022b4       0xb8 ./galaxy_sdk/drivers/src/hal_pdm.o
 .debug_frame   0x0000236c       0x40 ./galaxy_sdk/bsp/src/qemu_board.o
 .debug_frame   0x000023ac       0x80 ./galaxy_sdk/main.o
 .debug_frame   0x0000242c      0x168 ./galaxy_sdk/speaker_inference.o
 .debug_frame   0x00002594      0x200 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(port.c.o)
 .debug_frame   0x00002794       0x20 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_init.c.o)
 .debug_frame   0x000027b4      0x380 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(soc_sysctl.c.o)
 .debug_frame   0x00002b34       0xb4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(system_pegasus.c.o)
 .debug_frame   0x00002be8       0x50 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(board.c.o)
 .debug_frame   0x00002c38       0x9c D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\lib\libbsp_riscv.a(arch_api.c.o)
 .debug_frame   0x00002cd4      0x284 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(clock.c.o)
 .debug_frame   0x00002f58      0x1c4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\lib\libdriver_riscv.a(pdm_simulator.c.o)
 .debug_frame   0x0000311c      0x194 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(uart_printf.c.o)
 .debug_frame   0x000032b0      0x120 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(sw_timer_os.c.o)
 .debug_frame   0x000033d0       0x50 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vpi_error.c.o)
 .debug_frame   0x00003420       0x20 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\lib\libcommon_riscv.a(vs_logging.c.o)
 .debug_frame   0x00003440      0x118 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_task.c.o)
 .debug_frame   0x00003558       0x98 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_time.c.o)
 .debug_frame   0x000035f0      0x1a0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_timer.c.o)
 .debug_frame   0x00003790       0xc4 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_sys_state.c.o)
 .debug_frame   0x00003854       0x50 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\lib\libosal_riscv.a(osal_heap.c.o)
 .debug_frame   0x000038a4      0xbd0 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(tasks.c.o)
 .debug_frame   0x00004474       0x60 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(list.c.o)
 .debug_frame   0x000044d4      0x2a8 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(timers.c.o)
 .debug_frame   0x0000477c       0xfc D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(heap_4.c.o)
 .debug_frame   0x00004878      0x640 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\lib\libos_riscv.a(queue.c.o)
