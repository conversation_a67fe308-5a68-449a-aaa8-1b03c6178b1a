/*
 * SDK MFCC Feature Extraction Test Program
 * Using NMSIS DSP library for MFCC feature extraction
 */

#include <string.h>
#include <math.h>
#include "riscv_math.h"
#include "pdm_audio_02_data.h"
#include "mfcc_coefficients.h"

// MFCC instance and buffers
static riscv_mfcc_instance_f32 mfcc_instance;
static float32_t mfcc_temp_buffer[MFCC_N_FFT * 2];
static float32_t mfcc_output[MFCC_N_MFCC];

// Initialize MFCC instance
int init_mfcc_instance(void) {
    // Initialize MFCC instance with all required parameters
    riscv_status status = riscv_mfcc_init_f32(&mfcc_instance,
                                              MFCC_N_FFT,           // fftLen
                                              MFCC_N_MELS,          // nbMelFilters
                                              MFCC_N_MFCC,          // nbDctOutputs
                                              mfcc_dct_matrix,      // dctCoefs
                                              mfcc_filter_pos,      // filterPos
                                              mfcc_filter_len,      // filterLengths
                                              mfcc_filter_coeffs,   // filterCoefs
                                              mfcc_hamming_window); // windowCoefs

    if (status != RISCV_MATH_SUCCESS) {
        return -1;
    }

    return 0;
}

// 处理单帧音频
void process_single_frame(const float32_t* frame_data, int frame_length) {
    // 准备输入缓冲区（零填充到FFT长度）
    float32_t input_frame[MFCC_N_FFT];
    memset(input_frame, 0, sizeof(input_frame));
    memcpy(input_frame, frame_data, frame_length * sizeof(float32_t));

    // 执行MFCC特征提取（NMSIS DSP内部会处理归一化）
    riscv_mfcc_f32(&mfcc_instance, input_frame, mfcc_output, mfcc_temp_buffer);
}

// 处理多帧音频
void process_multiple_frames(const float32_t* audio_data, int total_samples) {
    int num_frames = (total_samples - MFCC_WIN_LENGTH) / MFCC_HOP_LENGTH + 1;

    // 存储所有帧的MFCC特征
    float32_t all_mfcc[num_frames][MFCC_N_MFCC];

    for (int frame = 0; frame < num_frames && frame < 10; frame++) { // 限制处理前10帧
        int start_sample = frame * MFCC_HOP_LENGTH;

        // 准备帧数据
        float32_t frame_data[MFCC_WIN_LENGTH];
        memcpy(frame_data, &audio_data[start_sample], MFCC_WIN_LENGTH * sizeof(float32_t));

        // 零填充输入缓冲区
        float32_t input_frame[MFCC_N_FFT];
        memset(input_frame, 0, sizeof(input_frame));
        memcpy(input_frame, frame_data, MFCC_WIN_LENGTH * sizeof(float32_t));

        // 执行MFCC提取（NMSIS DSP内部会处理归一化）
        riscv_mfcc_f32(&mfcc_instance, input_frame, mfcc_output, mfcc_temp_buffer);

        // 保存结果
        memcpy(all_mfcc[frame], mfcc_output, MFCC_N_MFCC * sizeof(float32_t));
    }

    // 计算前几帧的平均值（可通过调试器查看all_mfcc数组）
    int frames_to_avg = (num_frames < 5) ? num_frames : 5;

    // 平均值计算结果存储在all_mfcc数组中，可通过调试器查看
    (void)frames_to_avg; // 避免未使用变量警告
}

// 主测试函数
int test_sdk_mfcc(void) {
    // 初始化MFCC
    if (init_mfcc_instance() != 0) {
        return -1;
    }

    // 测试单帧处理（第一帧）
    process_single_frame(pdm_audio_02_float32, MFCC_WIN_LENGTH);

    // 测试多帧处理
    process_multiple_frames(pdm_audio_02_float32, AUDIO_SAMPLES);

    return 0;
}

// 主函数（如果作为独立程序运行）
#ifdef STANDALONE_TEST
int main(void) {
    return test_sdk_mfcc();
}
#endif
