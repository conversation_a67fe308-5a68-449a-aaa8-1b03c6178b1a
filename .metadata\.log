!SESSION 2025-08-02 23:53:20.809 -----------------------------------------------
eclipse.buildId=4.30.0.20231201-1200
java.version=17.0.9
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Framework arguments:  -product org.eclipse.epp.package.cpp.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.cpp.product

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-02 23:54:05.936
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libgcc_s_seh-1.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.loadManifest(ClasspathEntry.java:230)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.<init>(ClasspathEntry.java:77)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.createClassPathEntry(ModuleClassLoader.java:267)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.createClassPathEntry(ClasspathManager.java:343)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.getClasspath(ClasspathManager.java:295)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addStandardClassPathEntry(ClasspathManager.java:213)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addClassPathEntry(ClasspathManager.java:205)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassPathEntry(ClasspathManager.java:188)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.buildClasspath(ClasspathManager.java:166)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.<init>(ClasspathManager.java:94)
	at org.eclipse.osgi.internal.loader.EquinoxClassLoader.<init>(EquinoxClassLoader.java:54)
	at org.eclipse.osgi.internal.loader.BundleLoader.createClassLoaderPrivledged(BundleLoader.java:345)
	at org.eclipse.osgi.internal.loader.BundleLoader.getModuleClassLoader(BundleLoader.java:262)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:888)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:285)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:234)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:165)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:136)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:190)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:520)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:515)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.apache.aries.spifly.BaseActivator.getAllHeaders(BaseActivator.java:174)
	at org.apache.aries.spifly.BaseActivator.addConsumerWeavingData(BaseActivator.java:120)
	at org.apache.aries.spifly.ConsumerBundleTrackerCustomizer.addingBundle(ConsumerBundleTrackerCustomizer.java:37)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:477)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:258)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:185)
	at org.osgi.util.tracker.BundleTracker.open(BundleTracker.java:161)
	at org.apache.aries.spifly.BaseActivator.start(BaseActivator.java:103)
	at org.apache.aries.spifly.dynamic.DynamicWeavingActivator.start(DynamicWeavingActivator.java:37)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:818)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:810)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:767)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1032)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:371)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1852)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1845)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1788)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1750)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1672)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 55 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-02 23:54:05.957
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libstdc++-6.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.loadManifest(ClasspathEntry.java:230)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.<init>(ClasspathEntry.java:77)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.createClassPathEntry(ModuleClassLoader.java:267)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.createClassPathEntry(ClasspathManager.java:343)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.getClasspath(ClasspathManager.java:295)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addStandardClassPathEntry(ClasspathManager.java:213)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addClassPathEntry(ClasspathManager.java:205)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassPathEntry(ClasspathManager.java:188)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.buildClasspath(ClasspathManager.java:166)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.<init>(ClasspathManager.java:94)
	at org.eclipse.osgi.internal.loader.EquinoxClassLoader.<init>(EquinoxClassLoader.java:54)
	at org.eclipse.osgi.internal.loader.BundleLoader.createClassLoaderPrivledged(BundleLoader.java:345)
	at org.eclipse.osgi.internal.loader.BundleLoader.getModuleClassLoader(BundleLoader.java:262)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:888)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:285)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:234)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:165)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:136)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:190)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:520)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:515)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.apache.aries.spifly.BaseActivator.getAllHeaders(BaseActivator.java:174)
	at org.apache.aries.spifly.BaseActivator.addConsumerWeavingData(BaseActivator.java:120)
	at org.apache.aries.spifly.ConsumerBundleTrackerCustomizer.addingBundle(ConsumerBundleTrackerCustomizer.java:37)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:477)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:258)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:185)
	at org.osgi.util.tracker.BundleTracker.open(BundleTracker.java:161)
	at org.apache.aries.spifly.BaseActivator.start(BaseActivator.java:103)
	at org.apache.aries.spifly.dynamic.DynamicWeavingActivator.start(DynamicWeavingActivator.java:37)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:818)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:810)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:767)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1032)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:371)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1852)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1845)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1788)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1750)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1672)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 55 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-02 23:54:05.978
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libwinpthread-1.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.loadManifest(ClasspathEntry.java:230)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.<init>(ClasspathEntry.java:77)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.createClassPathEntry(ModuleClassLoader.java:267)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.createClassPathEntry(ClasspathManager.java:343)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.getClasspath(ClasspathManager.java:295)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addStandardClassPathEntry(ClasspathManager.java:213)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addClassPathEntry(ClasspathManager.java:205)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassPathEntry(ClasspathManager.java:188)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.buildClasspath(ClasspathManager.java:166)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.<init>(ClasspathManager.java:94)
	at org.eclipse.osgi.internal.loader.EquinoxClassLoader.<init>(EquinoxClassLoader.java:54)
	at org.eclipse.osgi.internal.loader.BundleLoader.createClassLoaderPrivledged(BundleLoader.java:345)
	at org.eclipse.osgi.internal.loader.BundleLoader.getModuleClassLoader(BundleLoader.java:262)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:888)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:285)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:234)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:165)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:136)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:190)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:520)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:515)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.apache.aries.spifly.BaseActivator.getAllHeaders(BaseActivator.java:174)
	at org.apache.aries.spifly.BaseActivator.addConsumerWeavingData(BaseActivator.java:120)
	at org.apache.aries.spifly.ConsumerBundleTrackerCustomizer.addingBundle(ConsumerBundleTrackerCustomizer.java:37)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:477)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:258)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:185)
	at org.osgi.util.tracker.BundleTracker.open(BundleTracker.java:161)
	at org.apache.aries.spifly.BaseActivator.start(BaseActivator.java:103)
	at org.apache.aries.spifly.dynamic.DynamicWeavingActivator.start(DynamicWeavingActivator.java:37)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:818)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:810)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:767)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1032)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:371)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1852)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1845)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1788)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1750)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1672)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 55 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-02 23:54:06.169
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\dqr.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.loadManifest(ClasspathEntry.java:230)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.<init>(ClasspathEntry.java:77)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.createClassPathEntry(ModuleClassLoader.java:267)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.createClassPathEntry(ClasspathManager.java:343)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.getClasspath(ClasspathManager.java:295)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addStandardClassPathEntry(ClasspathManager.java:213)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addClassPathEntry(ClasspathManager.java:205)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassPathEntry(ClasspathManager.java:188)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.buildClasspath(ClasspathManager.java:166)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.<init>(ClasspathManager.java:94)
	at org.eclipse.osgi.internal.loader.EquinoxClassLoader.<init>(EquinoxClassLoader.java:54)
	at org.eclipse.osgi.internal.loader.BundleLoader.createClassLoaderPrivledged(BundleLoader.java:345)
	at org.eclipse.osgi.internal.loader.BundleLoader.getModuleClassLoader(BundleLoader.java:262)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:888)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:285)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:234)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:165)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:136)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:190)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:520)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:515)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.apache.aries.spifly.BaseActivator.getAllHeaders(BaseActivator.java:174)
	at org.apache.aries.spifly.BaseActivator.addConsumerWeavingData(BaseActivator.java:120)
	at org.apache.aries.spifly.ConsumerBundleTrackerCustomizer.addingBundle(ConsumerBundleTrackerCustomizer.java:37)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:477)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:258)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:185)
	at org.osgi.util.tracker.BundleTracker.open(BundleTracker.java:161)
	at org.apache.aries.spifly.BaseActivator.start(BaseActivator.java:103)
	at org.apache.aries.spifly.dynamic.DynamicWeavingActivator.start(DynamicWeavingActivator.java:37)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:818)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:810)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:767)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1032)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:371)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1852)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1845)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1788)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1750)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1672)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 55 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-02 23:54:06.467
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libdqr.so
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.loadManifest(ClasspathEntry.java:230)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.<init>(ClasspathEntry.java:77)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.createClassPathEntry(ModuleClassLoader.java:267)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.createClassPathEntry(ClasspathManager.java:343)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.getClasspath(ClasspathManager.java:295)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addStandardClassPathEntry(ClasspathManager.java:213)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addClassPathEntry(ClasspathManager.java:205)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassPathEntry(ClasspathManager.java:188)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.buildClasspath(ClasspathManager.java:166)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.<init>(ClasspathManager.java:94)
	at org.eclipse.osgi.internal.loader.EquinoxClassLoader.<init>(EquinoxClassLoader.java:54)
	at org.eclipse.osgi.internal.loader.BundleLoader.createClassLoaderPrivledged(BundleLoader.java:345)
	at org.eclipse.osgi.internal.loader.BundleLoader.getModuleClassLoader(BundleLoader.java:262)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:888)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:285)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:234)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:165)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:136)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:190)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:520)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:515)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.apache.aries.spifly.BaseActivator.getAllHeaders(BaseActivator.java:174)
	at org.apache.aries.spifly.BaseActivator.addConsumerWeavingData(BaseActivator.java:120)
	at org.apache.aries.spifly.ConsumerBundleTrackerCustomizer.addingBundle(ConsumerBundleTrackerCustomizer.java:37)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:477)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:258)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:185)
	at org.osgi.util.tracker.BundleTracker.open(BundleTracker.java:161)
	at org.apache.aries.spifly.BaseActivator.start(BaseActivator.java:103)
	at org.apache.aries.spifly.dynamic.DynamicWeavingActivator.start(DynamicWeavingActivator.java:37)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:818)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:810)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:767)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1032)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:371)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1852)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1845)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1788)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1750)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1672)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 55 more

!ENTRY org.eclipse.ui 2 0 2025-08-02 23:54:42.420
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-02 23:54:42.420
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_System_Technology_Homepage', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-02 23:54:42.421
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Studio_User_Guide', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-02 23:54:42.421
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_SDK_Documentation', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-02 23:54:42.421
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Documentation', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-02 23:54:42.421
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Studio_FAQ', categoryId='org.riscvstudio.ide.rvtools.category'

!ENTRY org.eclipse.ui 2 0 2025-08-02 23:54:42.853
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-02 23:54:42.853
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_System_Technology_Homepage', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-02 23:54:42.853
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Studio_User_Guide', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-02 23:54:42.853
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_SDK_Documentation', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-02 23:54:42.853
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Documentation', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-02 23:54:42.853
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Studio_FAQ', categoryId='org.riscvstudio.ide.rvtools.category'

!ENTRY org.eclipse.egit.ui 2 0 2025-08-02 23:54:54.109
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libgcc_s_seh-1.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2024)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.listLocalResources(ClasspathManager.java:910)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.listLocalResources(ModuleClassLoader.java:393)
	at org.eclipse.osgi.internal.loader.BundleLoader.listResources(BundleLoader.java:873)
	at org.eclipse.osgi.container.ModuleWiring.listResources(ModuleWiring.java:300)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkClassResource(ModelCleanupAddon.java:356)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isPartDescriptorClassAvailable(ModelCleanupAddon.java:297)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkPartDescriptorByBundleSymbolicNameAndClass(ModelCleanupAddon.java:272)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isValidPartDescriptor(ModelCleanupAddon.java:240)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.getObsoletePartDescriptors(ModelCleanupAddon.java:140)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.lambda$0(ModelCleanupAddon.java:123)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 19 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-02 23:54:54.314
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libstdc++-6.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2024)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.listLocalResources(ClasspathManager.java:910)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.listLocalResources(ModuleClassLoader.java:393)
	at org.eclipse.osgi.internal.loader.BundleLoader.listResources(BundleLoader.java:873)
	at org.eclipse.osgi.container.ModuleWiring.listResources(ModuleWiring.java:300)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkClassResource(ModelCleanupAddon.java:356)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isPartDescriptorClassAvailable(ModelCleanupAddon.java:297)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkPartDescriptorByBundleSymbolicNameAndClass(ModelCleanupAddon.java:272)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isValidPartDescriptor(ModelCleanupAddon.java:240)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.getObsoletePartDescriptors(ModelCleanupAddon.java:140)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.lambda$0(ModelCleanupAddon.java:123)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 19 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-02 23:54:54.320
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libwinpthread-1.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2024)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.listLocalResources(ClasspathManager.java:910)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.listLocalResources(ModuleClassLoader.java:393)
	at org.eclipse.osgi.internal.loader.BundleLoader.listResources(BundleLoader.java:873)
	at org.eclipse.osgi.container.ModuleWiring.listResources(ModuleWiring.java:300)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkClassResource(ModelCleanupAddon.java:356)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isPartDescriptorClassAvailable(ModelCleanupAddon.java:297)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkPartDescriptorByBundleSymbolicNameAndClass(ModelCleanupAddon.java:272)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isValidPartDescriptor(ModelCleanupAddon.java:240)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.getObsoletePartDescriptors(ModelCleanupAddon.java:140)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.lambda$0(ModelCleanupAddon.java:123)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 19 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-02 23:54:54.322
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\dqr.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2024)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.listLocalResources(ClasspathManager.java:910)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.listLocalResources(ModuleClassLoader.java:393)
	at org.eclipse.osgi.internal.loader.BundleLoader.listResources(BundleLoader.java:873)
	at org.eclipse.osgi.container.ModuleWiring.listResources(ModuleWiring.java:300)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkClassResource(ModelCleanupAddon.java:356)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isPartDescriptorClassAvailable(ModelCleanupAddon.java:297)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkPartDescriptorByBundleSymbolicNameAndClass(ModelCleanupAddon.java:272)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isValidPartDescriptor(ModelCleanupAddon.java:240)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.getObsoletePartDescriptors(ModelCleanupAddon.java:140)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.lambda$0(ModelCleanupAddon.java:123)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 19 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-02 23:54:54.323
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libdqr.so
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2024)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.listLocalResources(ClasspathManager.java:910)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.listLocalResources(ModuleClassLoader.java:393)
	at org.eclipse.osgi.internal.loader.BundleLoader.listResources(BundleLoader.java:873)
	at org.eclipse.osgi.container.ModuleWiring.listResources(ModuleWiring.java:300)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkClassResource(ModelCleanupAddon.java:356)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isPartDescriptorClassAvailable(ModelCleanupAddon.java:297)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkPartDescriptorByBundleSymbolicNameAndClass(ModelCleanupAddon.java:272)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isValidPartDescriptor(ModelCleanupAddon.java:240)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.getObsoletePartDescriptors(ModelCleanupAddon.java:140)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.lambda$0(ModelCleanupAddon.java:123)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 19 more

!ENTRY org.eclipse.cdt.core 1 0 2025-08-02 23:55:43.285
!MESSAGE Indexed 'qemu' (6 sources, 308 headers) in 5.66 sec: 22,554 declarations; 29,571 references; 0 unresolved inclusions; 489 syntax errors; 477 unresolved names (0.91%)

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-02 23:56:34.480
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-02 23:56:35.781
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.core.expressions 4 201 2025-08-03 00:02:16.230
!MESSAGE No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project
!STACK 1
org.eclipse.core.runtime.CoreException: No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project
	at org.eclipse.core.internal.expressions.TypeExtensionManager.getProperty(TypeExtensionManager.java:132)
	at org.eclipse.core.expressions.TestExpression.evaluate(TestExpression.java:104)
	at org.eclipse.core.expressions.CompositeExpression.evaluateAnd(CompositeExpression.java:54)
	at org.eclipse.core.expressions.AndExpression.evaluate(AndExpression.java:36)
	at org.eclipse.ui.internal.dialogs.RegistryPageContributor.failsEnablement(RegistryPageContributor.java:284)
	at org.eclipse.ui.internal.dialogs.RegistryPageContributor.isApplicableTo(RegistryPageContributor.java:227)
	at org.eclipse.ui.internal.dialogs.PropertyPageContributorManager.getApplicableContributors(PropertyPageContributorManager.java:228)
	at org.eclipse.ui.internal.dialogs.PropertyPageContributorManager.getApplicableContributors(PropertyPageContributorManager.java:246)
	at org.eclipse.ui.dialogs.PropertyDialogAction.hasPropertyPagesFor(PropertyDialogAction.java:106)
	at org.eclipse.ui.dialogs.PropertyDialogAction.isApplicableForSelection(PropertyDialogAction.java:147)
	at org.eclipse.ui.dialogs.PropertyDialogAction.isApplicableForSelection(PropertyDialogAction.java:128)
	at org.eclipse.ui.internal.navigator.resources.actions.PropertiesActionProvider.fillContextMenu(PropertiesActionProvider.java:56)
	at org.eclipse.ui.navigator.NavigatorActionService$2.run(NavigatorActionService.java:228)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:47)
	at org.eclipse.ui.navigator.NavigatorActionService.addCommonActionProviderMenu(NavigatorActionService.java:222)
	at org.eclipse.ui.navigator.NavigatorActionService.fillContextMenu(NavigatorActionService.java:177)
	at org.eclipse.ui.navigator.CommonNavigatorManager.fillContextMenu(CommonNavigatorManager.java:259)
	at org.eclipse.jface.action.MenuManager.fireAboutToShow(MenuManager.java:338)
	at org.eclipse.jface.action.MenuManager.handleAboutToShow(MenuManager.java:468)
	at org.eclipse.jface.action.MenuManager$2.menuShown(MenuManager.java:495)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:259)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:89)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4273)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1066)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1090)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1071)
	at org.eclipse.swt.widgets.Control.WM_INITMENUPOPUP(Control.java:5141)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4773)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:340)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1478)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2305)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5039)
	at org.eclipse.swt.internal.win32.OS.TrackPopupMenu(Native Method)
	at org.eclipse.swt.widgets.Menu._setVisible(Menu.java:237)
	at org.eclipse.swt.widgets.Display.runPopups(Display.java:4112)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3653)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1155)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1046)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:155)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:648)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:555)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:152)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:208)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:136)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:402)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:255)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:651)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:588)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1459)
!SUBENTRY 1 org.eclipse.core.expressions 4 201 2025-08-03 00:02:16.230
!MESSAGE No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project

!ENTRY org.eclipse.core.expressions 4 201 2025-08-03 00:02:17.661
!MESSAGE No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project
!STACK 1
org.eclipse.core.runtime.CoreException: No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project
	at org.eclipse.core.internal.expressions.TypeExtensionManager.getProperty(TypeExtensionManager.java:132)
	at org.eclipse.core.expressions.TestExpression.evaluate(TestExpression.java:104)
	at org.eclipse.core.expressions.CompositeExpression.evaluateAnd(CompositeExpression.java:54)
	at org.eclipse.core.expressions.AndExpression.evaluate(AndExpression.java:36)
	at org.eclipse.ui.internal.dialogs.RegistryPageContributor.failsEnablement(RegistryPageContributor.java:284)
	at org.eclipse.ui.internal.dialogs.RegistryPageContributor.isApplicableTo(RegistryPageContributor.java:227)
	at org.eclipse.ui.internal.dialogs.PropertyPageContributorManager.contribute(PropertyPageContributorManager.java:114)
	at org.eclipse.ui.internal.dialogs.PropertyDialog.createDialogOn(PropertyDialog.java:64)
	at org.eclipse.ui.dialogs.PropertyDialogAction.createDialog(PropertyDialogAction.java:171)
	at org.eclipse.ui.dialogs.PropertyDialogAction.run(PropertyDialogAction.java:153)
	at org.eclipse.jface.action.Action.runWithEvent(Action.java:474)
	at org.eclipse.jface.action.ActionContributionItem.handleWidgetSelection(ActionContributionItem.java:580)
	at org.eclipse.jface.action.ActionContributionItem.lambda$4(ActionContributionItem.java:414)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:89)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4273)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1066)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4071)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3659)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1155)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1046)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:155)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:648)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:555)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:152)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:208)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:136)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:402)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:255)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:651)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:588)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1459)
!SUBENTRY 1 org.eclipse.core.expressions 4 201 2025-08-03 00:02:17.661
!MESSAGE No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project

!ENTRY org.eclipse.cdt.core 1 0 2025-08-03 00:06:05.817
!MESSAGE Indexed 'qemu' (37 sources, 373 headers) in 3.15 sec: 29,408 declarations; 48,862 references; 0 unresolved inclusions; 489 syntax errors; 486 unresolved names (0.62%)

!ENTRY org.eclipse.core.expressions 4 201 2025-08-03 00:08:12.925
!MESSAGE No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project
!STACK 1
org.eclipse.core.runtime.CoreException: No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project
	at org.eclipse.core.internal.expressions.TypeExtensionManager.getProperty(TypeExtensionManager.java:132)
	at org.eclipse.core.expressions.TestExpression.evaluate(TestExpression.java:104)
	at org.eclipse.core.expressions.CompositeExpression.evaluateAnd(CompositeExpression.java:54)
	at org.eclipse.core.expressions.AndExpression.evaluate(AndExpression.java:36)
	at org.eclipse.ui.internal.dialogs.RegistryPageContributor.failsEnablement(RegistryPageContributor.java:284)
	at org.eclipse.ui.internal.dialogs.RegistryPageContributor.isApplicableTo(RegistryPageContributor.java:227)
	at org.eclipse.ui.internal.dialogs.PropertyPageContributorManager.getApplicableContributors(PropertyPageContributorManager.java:228)
	at org.eclipse.ui.internal.dialogs.PropertyPageContributorManager.getApplicableContributors(PropertyPageContributorManager.java:246)
	at org.eclipse.ui.dialogs.PropertyDialogAction.hasPropertyPagesFor(PropertyDialogAction.java:106)
	at org.eclipse.ui.dialogs.PropertyDialogAction.isApplicableForSelection(PropertyDialogAction.java:147)
	at org.eclipse.ui.dialogs.PropertyDialogAction.isApplicableForSelection(PropertyDialogAction.java:128)
	at org.eclipse.ui.internal.navigator.resources.actions.PropertiesActionProvider.fillContextMenu(PropertiesActionProvider.java:56)
	at org.eclipse.ui.navigator.NavigatorActionService$2.run(NavigatorActionService.java:228)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:47)
	at org.eclipse.ui.navigator.NavigatorActionService.addCommonActionProviderMenu(NavigatorActionService.java:222)
	at org.eclipse.ui.navigator.NavigatorActionService.fillContextMenu(NavigatorActionService.java:177)
	at org.eclipse.ui.navigator.CommonNavigatorManager.fillContextMenu(CommonNavigatorManager.java:259)
	at org.eclipse.jface.action.MenuManager.fireAboutToShow(MenuManager.java:338)
	at org.eclipse.jface.action.MenuManager.handleAboutToShow(MenuManager.java:468)
	at org.eclipse.jface.action.MenuManager$2.menuShown(MenuManager.java:495)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:259)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:89)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4273)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1066)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1090)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1071)
	at org.eclipse.swt.widgets.Control.WM_INITMENUPOPUP(Control.java:5141)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4773)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:340)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1478)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2305)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5039)
	at org.eclipse.swt.internal.win32.OS.TrackPopupMenu(Native Method)
	at org.eclipse.swt.widgets.Menu._setVisible(Menu.java:237)
	at org.eclipse.swt.widgets.Display.runPopups(Display.java:4112)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3653)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1155)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1046)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:155)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:648)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:555)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:152)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:208)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:136)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:402)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:255)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:651)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:588)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1459)
!SUBENTRY 1 org.eclipse.core.expressions 4 201 2025-08-03 00:08:12.925
!MESSAGE No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project

!ENTRY org.eclipse.core.expressions 4 201 2025-08-03 00:08:14.503
!MESSAGE No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project
!STACK 1
org.eclipse.core.runtime.CoreException: No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project
	at org.eclipse.core.internal.expressions.TypeExtensionManager.getProperty(TypeExtensionManager.java:132)
	at org.eclipse.core.expressions.TestExpression.evaluate(TestExpression.java:104)
	at org.eclipse.core.expressions.CompositeExpression.evaluateAnd(CompositeExpression.java:54)
	at org.eclipse.core.expressions.AndExpression.evaluate(AndExpression.java:36)
	at org.eclipse.ui.internal.dialogs.RegistryPageContributor.failsEnablement(RegistryPageContributor.java:284)
	at org.eclipse.ui.internal.dialogs.RegistryPageContributor.isApplicableTo(RegistryPageContributor.java:227)
	at org.eclipse.ui.internal.dialogs.PropertyPageContributorManager.contribute(PropertyPageContributorManager.java:114)
	at org.eclipse.ui.internal.dialogs.PropertyDialog.createDialogOn(PropertyDialog.java:64)
	at org.eclipse.ui.dialogs.PropertyDialogAction.createDialog(PropertyDialogAction.java:171)
	at org.eclipse.ui.dialogs.PropertyDialogAction.run(PropertyDialogAction.java:153)
	at org.eclipse.jface.action.Action.runWithEvent(Action.java:474)
	at org.eclipse.jface.action.ActionContributionItem.handleWidgetSelection(ActionContributionItem.java:580)
	at org.eclipse.jface.action.ActionContributionItem.lambda$4(ActionContributionItem.java:414)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:89)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4273)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1066)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4071)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3659)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1155)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1046)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:155)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:648)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:555)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:152)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:208)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:136)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:402)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:255)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:651)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:588)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1459)
!SUBENTRY 1 org.eclipse.core.expressions 4 201 2025-08-03 00:08:14.503
!MESSAGE No property tester contributes a property ilg.gnumcueclipse.managedbuild.cross.isGnuMcu to type class org.eclipse.core.internal.resources.Project

!ENTRY org.eclipse.cdt.core 1 0 2025-08-03 00:09:40.065
!MESSAGE Indexed 'qemu' (37 sources, 373 headers) in 2.5 sec: 29,408 declarations; 48,862 references; 0 unresolved inclusions; 489 syntax errors; 486 unresolved names (0.62%)

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 01:42:09.319
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 01:42:09.699
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 14:18:11.589
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 14:18:12.642
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 14:39:18.726
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 14:39:19.169
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 20:03:38.916
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 20:03:40.116
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:09:56.644
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:09:57.578
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:15:17.517
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:15:17.944
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:23:04.890
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:23:05.242
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:23:56.010
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:23:56.362
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:27:58.829
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:27:59.182
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:28:24.467
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:28:24.829
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:29:02.875
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:29:03.252
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:30:06.068
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:30:06.423
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:32:23.702
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:32:24.055
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:53:13.717
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:53:14.097
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:59:16.978
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 21:59:17.336
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 22:06:40.806
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 22:06:41.162
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 22:08:40.026
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 22:08:40.402
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 22:09:12.569
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 22:09:12.926
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.cdt.dsf 4 10005 2025-08-03 22:10:46.406
!MESSAGE Request for monitor: 'RequestMonitor (org.eclipse.cdt.dsf.debug.ui.viewmodel.SteppingController$3@2150cddd): Status ERROR: org.eclipse.cdt.dsf.gdb code=10004 Failed to execute MI command:
-exec-next 1
Error message from debugger back end:
Cannot find bounds of current function java.lang.Exception: Cannot find bounds of current function' resulted in an error.
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-08-03 22:10:46.406
!MESSAGE Failed to execute MI command:
-exec-next 1
Error message from debugger back end:
Cannot find bounds of current function
!STACK 0
java.lang.Exception: Cannot find bounds of current function
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf 4 10005 2025-08-03 22:10:46.675
!MESSAGE Request for monitor: 'RequestMonitor (org.eclipse.cdt.dsf.debug.ui.viewmodel.SteppingController$3@5c21966c): Status ERROR: org.eclipse.cdt.dsf.gdb code=10004 Failed to execute MI command:
-exec-next 1
Error message from debugger back end:
Cannot find bounds of current function java.lang.Exception: Cannot find bounds of current function' resulted in an error.
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-08-03 22:10:46.675
!MESSAGE Failed to execute MI command:
-exec-next 1
Error message from debugger back end:
Cannot find bounds of current function
!STACK 0
java.lang.Exception: Cannot find bounds of current function
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf 4 10005 2025-08-03 22:10:49.537
!MESSAGE Request for monitor: 'RequestMonitor (org.eclipse.cdt.dsf.debug.ui.viewmodel.SteppingController$3@af360bd): Status ERROR: org.eclipse.cdt.dsf.gdb code=10004 Failed to execute MI command:
-exec-next 1
Error message from debugger back end:
Cannot find bounds of current function java.lang.Exception: Cannot find bounds of current function' resulted in an error.
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-08-03 22:10:49.537
!MESSAGE Failed to execute MI command:
-exec-next 1
Error message from debugger back end:
Cannot find bounds of current function
!STACK 0
java.lang.Exception: Cannot find bounds of current function
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf 4 10005 2025-08-03 22:10:49.778
!MESSAGE Request for monitor: 'RequestMonitor (org.eclipse.cdt.dsf.debug.ui.viewmodel.SteppingController$3@39c00f3c): Status ERROR: org.eclipse.cdt.dsf.gdb code=10004 Failed to execute MI command:
-exec-next 1
Error message from debugger back end:
Cannot find bounds of current function java.lang.Exception: Cannot find bounds of current function' resulted in an error.
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-08-03 22:10:49.778
!MESSAGE Failed to execute MI command:
-exec-next 1
Error message from debugger back end:
Cannot find bounds of current function
!STACK 0
java.lang.Exception: Cannot find bounds of current function
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf 4 10005 2025-08-03 22:10:50.125
!MESSAGE Request for monitor: 'RequestMonitor (org.eclipse.cdt.dsf.debug.ui.viewmodel.SteppingController$3@47614e76): Status ERROR: org.eclipse.cdt.dsf.gdb code=10004 Failed to execute MI command:
-exec-next 1
Error message from debugger back end:
Cannot find bounds of current function java.lang.Exception: Cannot find bounds of current function' resulted in an error.
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-08-03 22:10:50.125
!MESSAGE Failed to execute MI command:
-exec-next 1
Error message from debugger back end:
Cannot find bounds of current function
!STACK 0
java.lang.Exception: Cannot find bounds of current function
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.ui 2 0 2025-08-03 22:31:41.672
!MESSAGE Unable to locate file: /home/<USER>/workspace/workspace/cocopalmR/modules/logging/uart_printf.c

!ENTRY org.eclipse.cdt.dsf.ui 2 0 2025-08-03 22:31:42.587
!MESSAGE Unable to locate file: /home/<USER>/workspace/workspace/cocopalmR/drivers/pdm/pdm_simulator.c

!ENTRY org.eclipse.cdt.dsf.ui 2 0 2025-08-03 22:32:02.728
!MESSAGE Unable to locate file: /home/<USER>/workspace/workspace/cocopalmR/modules/sw_timer/sw_timer_os.c

!ENTRY org.eclipse.cdt.dsf.ui 2 0 2025-08-03 22:32:40.847
!MESSAGE Unable to locate file: /home/<USER>/workspace/workspace/cocopalmR/modules/error/vpi_error.c

!ENTRY org.eclipse.cdt.dsf 4 10005 2025-08-03 22:34:44.381
!MESSAGE Request for monitor: 'RequestMonitor (org.eclipse.cdt.dsf.debug.ui.viewmodel.SteppingController$3@37279a58): Status ERROR: org.eclipse.cdt.dsf.gdb code=10004 Failed to execute MI command:
-exec-step 1
Error message from debugger back end:
Cannot find bounds of current function java.lang.Exception: Cannot find bounds of current function' resulted in an error.
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-08-03 22:34:44.381
!MESSAGE Failed to execute MI command:
-exec-step 1
Error message from debugger back end:
Cannot find bounds of current function
!STACK 0
java.lang.Exception: Cannot find bounds of current function
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf 4 10005 2025-08-03 22:34:47.107
!MESSAGE Request for monitor: 'RequestMonitor (org.eclipse.cdt.dsf.debug.ui.viewmodel.SteppingController$3@7408a1aa): Status ERROR: org.eclipse.cdt.dsf.gdb code=10004 Failed to execute MI command:
-exec-step 1
Error message from debugger back end:
Cannot find bounds of current function java.lang.Exception: Cannot find bounds of current function' resulted in an error.
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-08-03 22:34:47.107
!MESSAGE Failed to execute MI command:
-exec-step 1
Error message from debugger back end:
Cannot find bounds of current function
!STACK 0
java.lang.Exception: Cannot find bounds of current function
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 22:41:04.493
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 22:41:04.871
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.ui 4 0 2025-08-03 22:42:28.504
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.core.resources.IResource.getAdapter(java.lang.Class)" because "resource" is null
	at org.eclipse.linuxtools.internal.changelog.core.editors.GNUHyperlinkDetector.detectHyperlinks(GNUHyperlinkDetector.java:120)
	at org.eclipse.ui.texteditor.HyperlinkDetectorRegistry$HyperlinkDetectorDelegate.detectHyperlinks(HyperlinkDetectorRegistry.java:81)
	at org.eclipse.jface.text.hyperlink.HyperlinkManager.findHyperlinks(HyperlinkManager.java:289)
	at org.eclipse.jface.text.hyperlink.HyperlinkManager.findHyperlinks(HyperlinkManager.java:262)
	at org.eclipse.jface.text.hyperlink.HyperlinkManager.mouseMove(HyperlinkManager.java:457)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:216)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:89)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4273)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1066)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4071)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3659)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1155)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1046)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:155)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:648)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:555)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:152)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:208)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:136)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:402)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:255)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:651)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:588)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1459)

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 23:42:06.977
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 23:42:07.714
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.cdt.dsf 4 10001 2025-08-03 23:42:59.183
!MESSAGE Target not available.

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 23:43:38.481
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 23:43:38.844
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 23:52:47.180
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-03 23:52:47.575
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 00:10:38.156
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 00:10:38.529
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.jface 2 0 2025-08-04 00:13:37.973
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2025-08-04 00:13:37.973
!MESSAGE A conflict occurred for CTRL+G:
Binding(CTRL+G,
	ParameterizedCommand(Command(org.eclipse.cdt.ui.search.finddecl,Declaration,
		Searches for declarations of the selected element in the workspace,
		Category(org.eclipse.cdt.ui.category.source,C/C++ Source,C/C++ Source Actions,true),
		WorkbenchHandlerServiceHandler("org.eclipse.cdt.ui.search.finddecl"),
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.cdt.ui.cEditorScope,,,system)
Binding(CTRL+G,
	ParameterizedCommand(Command(org.eclipse.debug.ui.command.gotoaddress,Go to Address,
		Go to Address,
		Category(org.eclipse.debug.ui.category.run,Run/Debug,Run/Debug command category,true),
		,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.debug.ui.memory.abstractasynctablerendering,,,system)

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-04 00:17:20.748
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-04 00:19:51.021
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-04 00:23:09.414
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-04 00:26:28.025
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.ui 4 0 2025-08-04 00:44:17.880
!MESSAGE Unhandled event loop exception
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4918)
	at org.eclipse.swt.SWT.error(SWT.java:4833)
	at org.eclipse.swt.SWT.error(SWT.java:4804)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:450)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:369)
	at org.eclipse.swt.widgets.Control.isVisible(Control.java:1999)
	at org.eclipse.cdt.debug.ui.memory.traditional.RenderingAddressInfo$1.lambda$0(RenderingAddressInfo.java:263)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4046)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3662)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1155)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1046)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:155)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:648)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:555)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:152)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:208)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:136)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:402)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:255)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:651)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:588)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1459)

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 00:56:47.489
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 00:56:47.879
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 00:57:45.271
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 00:57:45.637
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:22:32.136
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:22:32.533
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:36:34.061
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:36:34.454
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:38:21.226
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:38:21.599
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:54:53.985
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:54:54.378
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.core 4 0 2025-08-04 01:55:09.729
!MESSAGE Debug session 'debug_qemu' already started. Terminate the first one before restarting.

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:55:15.642
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:55:16.007
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:58:24.549
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 01:58:24.916
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 02:01:30.894
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 02:01:31.323
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 02:19:59.505
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 02:19:59.942
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 22:26:09.388
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 22:26:10.787
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:09:44.602
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:09:45.004
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:13:26.021
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:13:26.385
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:15:27.903
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:15:28.311
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:17:54.768
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:17:55.182
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:21:09.259
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:21:09.619
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:23:18.503
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:23:18.930
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:25:27.645
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:25:28.015
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:27:20.274
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:27:20.640
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:29:17.028
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:29:17.433
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:29:52.309
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:29:52.672
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:30:10.150
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:30:10.513
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:36:33.382
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:36:33.773
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:45:08.233
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:45:08.669
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:45:46.388
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:45:46.787
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:46:19.546
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:46:19.923
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:47:12.680
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:47:13.050
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:47:49.070
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:47:49.443
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:49:02.410
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:49:02.778
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:49:49.542
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:49:49.907
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:50:23.987
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:50:24.360
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:51:18.161
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:51:18.530
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:52:33.173
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-04 23:52:33.542
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-05 01:57:00.639
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.packs.core 4 1 2025-08-05 01:57:01.439
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx
!SESSION 2025-08-05 17:40:21.308 -----------------------------------------------
eclipse.buildId=4.30.0.20231201-1200
java.version=17.0.9
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Framework arguments:  -product org.eclipse.epp.package.cpp.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.cpp.product

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-05 17:41:34.392
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libgcc_s_seh-1.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.loadManifest(ClasspathEntry.java:230)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.<init>(ClasspathEntry.java:77)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.createClassPathEntry(ModuleClassLoader.java:267)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.createClassPathEntry(ClasspathManager.java:343)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.getClasspath(ClasspathManager.java:295)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addStandardClassPathEntry(ClasspathManager.java:213)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addClassPathEntry(ClasspathManager.java:205)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassPathEntry(ClasspathManager.java:188)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.buildClasspath(ClasspathManager.java:166)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.<init>(ClasspathManager.java:94)
	at org.eclipse.osgi.internal.loader.EquinoxClassLoader.<init>(EquinoxClassLoader.java:54)
	at org.eclipse.osgi.internal.loader.BundleLoader.createClassLoaderPrivledged(BundleLoader.java:345)
	at org.eclipse.osgi.internal.loader.BundleLoader.getModuleClassLoader(BundleLoader.java:262)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:888)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:285)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:234)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:165)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:136)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:190)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:520)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:515)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.apache.aries.spifly.BaseActivator.getAllHeaders(BaseActivator.java:174)
	at org.apache.aries.spifly.BaseActivator.addConsumerWeavingData(BaseActivator.java:120)
	at org.apache.aries.spifly.ConsumerBundleTrackerCustomizer.addingBundle(ConsumerBundleTrackerCustomizer.java:37)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:477)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:258)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:185)
	at org.osgi.util.tracker.BundleTracker.open(BundleTracker.java:161)
	at org.apache.aries.spifly.BaseActivator.start(BaseActivator.java:103)
	at org.apache.aries.spifly.dynamic.DynamicWeavingActivator.start(DynamicWeavingActivator.java:37)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:818)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:810)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:767)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1032)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:371)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1852)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1845)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1788)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1750)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1672)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 55 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-05 17:41:34.439
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libstdc++-6.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.loadManifest(ClasspathEntry.java:230)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.<init>(ClasspathEntry.java:77)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.createClassPathEntry(ModuleClassLoader.java:267)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.createClassPathEntry(ClasspathManager.java:343)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.getClasspath(ClasspathManager.java:295)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addStandardClassPathEntry(ClasspathManager.java:213)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addClassPathEntry(ClasspathManager.java:205)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassPathEntry(ClasspathManager.java:188)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.buildClasspath(ClasspathManager.java:166)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.<init>(ClasspathManager.java:94)
	at org.eclipse.osgi.internal.loader.EquinoxClassLoader.<init>(EquinoxClassLoader.java:54)
	at org.eclipse.osgi.internal.loader.BundleLoader.createClassLoaderPrivledged(BundleLoader.java:345)
	at org.eclipse.osgi.internal.loader.BundleLoader.getModuleClassLoader(BundleLoader.java:262)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:888)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:285)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:234)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:165)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:136)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:190)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:520)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:515)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.apache.aries.spifly.BaseActivator.getAllHeaders(BaseActivator.java:174)
	at org.apache.aries.spifly.BaseActivator.addConsumerWeavingData(BaseActivator.java:120)
	at org.apache.aries.spifly.ConsumerBundleTrackerCustomizer.addingBundle(ConsumerBundleTrackerCustomizer.java:37)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:477)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:258)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:185)
	at org.osgi.util.tracker.BundleTracker.open(BundleTracker.java:161)
	at org.apache.aries.spifly.BaseActivator.start(BaseActivator.java:103)
	at org.apache.aries.spifly.dynamic.DynamicWeavingActivator.start(DynamicWeavingActivator.java:37)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:818)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:810)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:767)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1032)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:371)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1852)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1845)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1788)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1750)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1672)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 55 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-05 17:41:34.440
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libwinpthread-1.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.loadManifest(ClasspathEntry.java:230)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.<init>(ClasspathEntry.java:77)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.createClassPathEntry(ModuleClassLoader.java:267)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.createClassPathEntry(ClasspathManager.java:343)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.getClasspath(ClasspathManager.java:295)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addStandardClassPathEntry(ClasspathManager.java:213)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addClassPathEntry(ClasspathManager.java:205)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassPathEntry(ClasspathManager.java:188)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.buildClasspath(ClasspathManager.java:166)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.<init>(ClasspathManager.java:94)
	at org.eclipse.osgi.internal.loader.EquinoxClassLoader.<init>(EquinoxClassLoader.java:54)
	at org.eclipse.osgi.internal.loader.BundleLoader.createClassLoaderPrivledged(BundleLoader.java:345)
	at org.eclipse.osgi.internal.loader.BundleLoader.getModuleClassLoader(BundleLoader.java:262)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:888)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:285)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:234)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:165)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:136)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:190)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:520)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:515)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.apache.aries.spifly.BaseActivator.getAllHeaders(BaseActivator.java:174)
	at org.apache.aries.spifly.BaseActivator.addConsumerWeavingData(BaseActivator.java:120)
	at org.apache.aries.spifly.ConsumerBundleTrackerCustomizer.addingBundle(ConsumerBundleTrackerCustomizer.java:37)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:477)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:258)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:185)
	at org.osgi.util.tracker.BundleTracker.open(BundleTracker.java:161)
	at org.apache.aries.spifly.BaseActivator.start(BaseActivator.java:103)
	at org.apache.aries.spifly.dynamic.DynamicWeavingActivator.start(DynamicWeavingActivator.java:37)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:818)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:810)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:767)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1032)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:371)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1852)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1845)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1788)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1750)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1672)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 55 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-05 17:41:34.440
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\dqr.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.loadManifest(ClasspathEntry.java:230)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.<init>(ClasspathEntry.java:77)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.createClassPathEntry(ModuleClassLoader.java:267)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.createClassPathEntry(ClasspathManager.java:343)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.getClasspath(ClasspathManager.java:295)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addStandardClassPathEntry(ClasspathManager.java:213)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addClassPathEntry(ClasspathManager.java:205)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassPathEntry(ClasspathManager.java:188)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.buildClasspath(ClasspathManager.java:166)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.<init>(ClasspathManager.java:94)
	at org.eclipse.osgi.internal.loader.EquinoxClassLoader.<init>(EquinoxClassLoader.java:54)
	at org.eclipse.osgi.internal.loader.BundleLoader.createClassLoaderPrivledged(BundleLoader.java:345)
	at org.eclipse.osgi.internal.loader.BundleLoader.getModuleClassLoader(BundleLoader.java:262)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:888)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:285)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:234)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:165)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:136)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:190)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:520)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:515)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.apache.aries.spifly.BaseActivator.getAllHeaders(BaseActivator.java:174)
	at org.apache.aries.spifly.BaseActivator.addConsumerWeavingData(BaseActivator.java:120)
	at org.apache.aries.spifly.ConsumerBundleTrackerCustomizer.addingBundle(ConsumerBundleTrackerCustomizer.java:37)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:477)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:258)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:185)
	at org.osgi.util.tracker.BundleTracker.open(BundleTracker.java:161)
	at org.apache.aries.spifly.BaseActivator.start(BaseActivator.java:103)
	at org.apache.aries.spifly.dynamic.DynamicWeavingActivator.start(DynamicWeavingActivator.java:37)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:818)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:810)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:767)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1032)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:371)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1852)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1845)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1788)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1750)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1672)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 55 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-05 17:41:36.698
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libdqr.so
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.loadManifest(ClasspathEntry.java:230)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathEntry.<init>(ClasspathEntry.java:77)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.createClassPathEntry(ModuleClassLoader.java:267)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.createClassPathEntry(ClasspathManager.java:343)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.getClasspath(ClasspathManager.java:295)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addStandardClassPathEntry(ClasspathManager.java:213)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.addClassPathEntry(ClasspathManager.java:205)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassPathEntry(ClasspathManager.java:188)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.buildClasspath(ClasspathManager.java:166)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.<init>(ClasspathManager.java:94)
	at org.eclipse.osgi.internal.loader.EquinoxClassLoader.<init>(EquinoxClassLoader.java:54)
	at org.eclipse.osgi.internal.loader.BundleLoader.createClassLoaderPrivledged(BundleLoader.java:345)
	at org.eclipse.osgi.internal.loader.BundleLoader.getModuleClassLoader(BundleLoader.java:262)
	at org.eclipse.osgi.internal.loader.BundleLoader.findEntries(BundleLoader.java:888)
	at org.eclipse.osgi.container.ModuleWiring.findEntries(ModuleWiring.java:285)
	at org.eclipse.osgi.storage.ManifestLocalization.findResource(ManifestLocalization.java:234)
	at org.eclipse.osgi.storage.ManifestLocalization.lookupResourceBundle(ManifestLocalization.java:165)
	at org.eclipse.osgi.storage.ManifestLocalization.getResourceBundle(ManifestLocalization.java:136)
	at org.eclipse.osgi.storage.ManifestLocalization.getHeaders(ManifestLocalization.java:78)
	at org.eclipse.osgi.storage.BundleInfo$Generation.getHeaders(BundleInfo.java:190)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.privGetHeaders(EquinoxBundle.java:520)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:515)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.getHeaders(EquinoxBundle.java:509)
	at org.apache.aries.spifly.BaseActivator.getAllHeaders(BaseActivator.java:174)
	at org.apache.aries.spifly.BaseActivator.addConsumerWeavingData(BaseActivator.java:120)
	at org.apache.aries.spifly.ConsumerBundleTrackerCustomizer.addingBundle(ConsumerBundleTrackerCustomizer.java:37)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:477)
	at org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(BundleTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:258)
	at org.osgi.util.tracker.AbstractTracked.trackInitial(AbstractTracked.java:185)
	at org.osgi.util.tracker.BundleTracker.open(BundleTracker.java:161)
	at org.apache.aries.spifly.BaseActivator.start(BaseActivator.java:103)
	at org.apache.aries.spifly.dynamic.DynamicWeavingActivator.start(DynamicWeavingActivator.java:37)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:818)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$2.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:810)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:767)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1032)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:371)
	at org.eclipse.osgi.container.Module.doStart(Module.java:605)
	at org.eclipse.osgi.container.Module.start(Module.java:468)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run(ModuleContainer.java:1852)
	at org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(EquinoxContainerAdaptor.java:136)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1845)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1788)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1750)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1672)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:234)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:345)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 55 more

!ENTRY org.eclipse.ui 2 0 2025-08-05 17:44:27.552
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-05 17:44:27.552
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_System_Technology_Homepage', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-05 17:44:27.552
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Studio_User_Guide', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-05 17:44:27.552
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_SDK_Documentation', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-05 17:44:27.552
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Documentation', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-05 17:44:27.552
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Studio_FAQ', categoryId='org.riscvstudio.ide.rvtools.category'

!ENTRY org.eclipse.ui 2 0 2025-08-05 17:44:29.067
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-05 17:44:29.067
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_System_Technology_Homepage', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-05 17:44:29.067
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Studio_User_Guide', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-05 17:44:29.068
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_SDK_Documentation', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-05 17:44:29.068
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Documentation', categoryId='org.riscvstudio.ide.rvtools.category'
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-05 17:44:29.068
!MESSAGE Commands should really have a category: plug-in='org.riscvstudio.ide', id='org.riscvstudio.ide.commands.Nuclei_Studio_FAQ', categoryId='org.riscvstudio.ide.rvtools.category'

!ENTRY org.eclipse.egit.ui 2 0 2025-08-05 17:44:55.781
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libgcc_s_seh-1.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2024)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.listLocalResources(ClasspathManager.java:910)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.listLocalResources(ModuleClassLoader.java:393)
	at org.eclipse.osgi.internal.loader.BundleLoader.listResources(BundleLoader.java:873)
	at org.eclipse.osgi.container.ModuleWiring.listResources(ModuleWiring.java:300)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkClassResource(ModelCleanupAddon.java:356)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isPartDescriptorClassAvailable(ModelCleanupAddon.java:297)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkPartDescriptorByBundleSymbolicNameAndClass(ModelCleanupAddon.java:272)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isValidPartDescriptor(ModelCleanupAddon.java:240)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.getObsoletePartDescriptors(ModelCleanupAddon.java:140)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.lambda$0(ModelCleanupAddon.java:123)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 19 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-05 17:44:57.323
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libstdc++-6.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2024)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.listLocalResources(ClasspathManager.java:910)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.listLocalResources(ModuleClassLoader.java:393)
	at org.eclipse.osgi.internal.loader.BundleLoader.listResources(BundleLoader.java:873)
	at org.eclipse.osgi.container.ModuleWiring.listResources(ModuleWiring.java:300)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkClassResource(ModelCleanupAddon.java:356)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isPartDescriptorClassAvailable(ModelCleanupAddon.java:297)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkPartDescriptorByBundleSymbolicNameAndClass(ModelCleanupAddon.java:272)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isValidPartDescriptor(ModelCleanupAddon.java:240)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.getObsoletePartDescriptors(ModelCleanupAddon.java:140)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.lambda$0(ModelCleanupAddon.java:123)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 19 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-05 17:44:57.481
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libwinpthread-1.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2024)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.listLocalResources(ClasspathManager.java:910)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.listLocalResources(ModuleClassLoader.java:393)
	at org.eclipse.osgi.internal.loader.BundleLoader.listResources(BundleLoader.java:873)
	at org.eclipse.osgi.container.ModuleWiring.listResources(ModuleWiring.java:300)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkClassResource(ModelCleanupAddon.java:356)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isPartDescriptorClassAvailable(ModelCleanupAddon.java:297)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkPartDescriptorByBundleSymbolicNameAndClass(ModelCleanupAddon.java:272)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isValidPartDescriptor(ModelCleanupAddon.java:240)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.getObsoletePartDescriptors(ModelCleanupAddon.java:140)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.lambda$0(ModelCleanupAddon.java:123)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 19 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-05 17:44:57.897
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\dqr.dll
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2024)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.listLocalResources(ClasspathManager.java:910)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.listLocalResources(ModuleClassLoader.java:393)
	at org.eclipse.osgi.internal.loader.BundleLoader.listResources(BundleLoader.java:873)
	at org.eclipse.osgi.container.ModuleWiring.listResources(ModuleWiring.java:300)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkClassResource(ModelCleanupAddon.java:356)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isPartDescriptorClassAvailable(ModelCleanupAddon.java:297)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkPartDescriptorByBundleSymbolicNameAndClass(ModelCleanupAddon.java:272)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isValidPartDescriptor(ModelCleanupAddon.java:240)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.getObsoletePartDescriptors(ModelCleanupAddon.java:140)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.lambda$0(ModelCleanupAddon.java:123)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 19 more

!ENTRY org.riscvstudio.ide.tools.trace 4 0 2025-08-05 17:44:58.044
!MESSAGE FrameworkEvent ERROR
!STACK 0
java.util.zip.ZipException: Exception in opening zip file: D:\NucleiStudio\NucleiStudio\configuration\org.eclipse.osgi\620\0\.cp\lib\libdqr.so
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:356)
	at org.eclipse.osgi.storage.bundlefile.ZipBundleFile.doOpen(ZipBundleFile.java:51)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.internalOpen(CloseableBundleFile.java:140)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.lockOpen(CloseableBundleFile.java:78)
	at org.eclipse.osgi.storage.bundlefile.CloseableBundleFile.getEntry(CloseableBundleFile.java:274)
	at org.eclipse.osgi.storage.Storage.listEntryPaths(Storage.java:2024)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.listLocalResources(ClasspathManager.java:910)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.listLocalResources(ModuleClassLoader.java:393)
	at org.eclipse.osgi.internal.loader.BundleLoader.listResources(BundleLoader.java:873)
	at org.eclipse.osgi.container.ModuleWiring.listResources(ModuleWiring.java:300)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkClassResource(ModelCleanupAddon.java:356)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isPartDescriptorClassAvailable(ModelCleanupAddon.java:297)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.checkPartDescriptorByBundleSymbolicNameAndClass(ModelCleanupAddon.java:272)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.isValidPartDescriptor(ModelCleanupAddon.java:240)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.getObsoletePartDescriptors(ModelCleanupAddon.java:140)
	at org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon.lambda$0(ModelCleanupAddon.java:123)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.util.zip.ZipException: zip END header not found
	at java.base/java.util.zip.ZipFile$Source.findEND(ZipFile.java:1633)
	at java.base/java.util.zip.ZipFile$Source.initCEN(ZipFile.java:1641)
	at java.base/java.util.zip.ZipFile$Source.<init>(ZipFile.java:1479)
	at java.base/java.util.zip.ZipFile$Source.get(ZipFile.java:1441)
	at java.base/java.util.zip.ZipFile$CleanableResource.<init>(ZipFile.java:718)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:252)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:181)
	at java.base/java.util.zip.ZipFile.<init>(ZipFile.java:195)
	at org.eclipse.osgi.framework.util.SecureAction.getZipFile(SecureAction.java:342)
	... 19 more

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-05 17:47:41.986
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-05 17:47:43.118
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.ui 4 0 2025-08-05 17:49:12.368
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.core.resources.IResource.getAdapter(java.lang.Class)" because "resource" is null
	at org.eclipse.linuxtools.internal.changelog.core.editors.GNUHyperlinkDetector.detectHyperlinks(GNUHyperlinkDetector.java:120)
	at org.eclipse.ui.texteditor.HyperlinkDetectorRegistry$HyperlinkDetectorDelegate.detectHyperlinks(HyperlinkDetectorRegistry.java:81)
	at org.eclipse.jface.text.hyperlink.HyperlinkManager.findHyperlinks(HyperlinkManager.java:289)
	at org.eclipse.jface.text.hyperlink.HyperlinkManager.findHyperlinks(HyperlinkManager.java:262)
	at org.eclipse.jface.text.hyperlink.HyperlinkManager.mouseMove(HyperlinkManager.java:457)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:216)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:89)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4273)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1066)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4071)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3659)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1155)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1046)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:155)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:648)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:342)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:555)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:152)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:208)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:136)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:402)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:255)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:651)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:588)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1459)

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.core 4 0 2025-08-05 17:53:52.908
!MESSAGE Debug session 'debug_qemu' already started. Terminate the first one before restarting.

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-05 17:53:57.389
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-05 17:53:57.736
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-05 17:54:15.599
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-05 17:54:22.294
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-05 17:54:31.135
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-05 17:57:18.984
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-05 17:58:08.225
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-05 17:59:03.067
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.cdt.dsf.gdb 4 4 2025-08-05 18:01:53.964
!MESSAGE NullPointerException while trying to calculated source lookup path. This can be ignored if it occurs while terminating a debug session. See Bug 500988.
!STACK 0
java.lang.NullPointerException: Cannot read the array length because "containers" is null
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:102)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.collectSubstitutionsPaths(GdbSourceLookupDirector.java:146)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupDirector.getSubstitutionsPaths(GdbSourceLookupDirector.java:72)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.getSubstitutionsPaths(GDBSourceLookup.java:124)
	at org.eclipse.cdt.dsf.gdb.service.GDBSourceLookup.sourceContainersChanged(GDBSourceLookup.java:139)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant.sourceContainersChangedOnDispatchThread(GdbSourceLookupParticipant.java:120)
	at org.eclipse.cdt.dsf.gdb.launching.GdbSourceLookupParticipant$1.run(GdbSourceLookupParticipant.java:85)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-06 01:46:54.019
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-06 01:46:54.360
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-06 01:48:05.065
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-06 01:48:05.402
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-06 01:58:08.568
!MESSAGE D:\NucleiStudio\NucleiStudio\\toolchain\qemu\bin/qemu-system-riscv32.exe -machine nuclei_evalsoc,download=flashxip -cpu nuclei-n307,ext=_xxldsp -smp 1 -gdb tcp::1234 -serial stdio -nodefaults -S -nographic --semihosting-config enable=on,target=native

!ENTRY org.eclipse.embedcdt.debug.gdbjtag.qemu.core 4 1 2025-08-06 01:58:08.921
!MESSAGE riscv64-unknown-elf-gdb --interpreter=mi2 --nx
