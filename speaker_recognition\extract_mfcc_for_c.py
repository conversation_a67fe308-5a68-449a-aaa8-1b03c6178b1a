#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MFCC特征提取脚本 - 为C代码生成真实MFCC数据

作者: Augment Agent
日期: 2025-08-03

功能:
- 从指定音频文件提取MFCC特征
- 使用与训练时完全一致的SpeakerFeatureExtractor
- 生成C语言数组格式的头文件
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 设置正常显示符号

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入我们的特征提取器
from feature_extractor import SpeakerFeatureExtractor, SAMPLE_RATE

# 目标尺寸
TARGET_FRAMES = 198    # 时间帧数
TARGET_MFCC_DIM = 12   # MFCC维度 (去除C0后)

def extract_mfcc_from_audio(audio_path):
    """
    使用SpeakerFeatureExtractor从音频文件提取MFCC特征
    确保与训练时完全一致

    Args:
        audio_path: 音频文件路径

    Returns:
        mfcc_features: MFCC特征数组 [frames, mfcc_dim] (198, 12)
    """
    print(f"🎵 处理音频文件: {audio_path}")

    try:
        # 创建特征提取器 (与训练时完全一致)
        extractor = SpeakerFeatureExtractor()

        # 提取完整13维MFCC特征
        print(f"🔧 使用SpeakerFeatureExtractor提取MFCC特征...")
        mfcc_feat = extractor.extract_mfcc_from_file(audio_path)

        if mfcc_feat is None:
            print(f"❌ MFCC特征提取失败")
            return None

        print(f"   完整MFCC形状: {mfcc_feat.shape}")
        print(f"   完整MFCC范围: [{mfcc_feat.min():.6f}, {mfcc_feat.max():.6f}]")

        # 去除C0系数 (与训练时保持一致)
        mfcc_no_c0 = mfcc_feat[:, 1:]  # (198, 12)
        print(f"   去除C0后形状: {mfcc_no_c0.shape}")
        print(f"   去除C0后范围: [{mfcc_no_c0.min():.6f}, {mfcc_no_c0.max():.6f}]")

        # 验证尺寸
        if mfcc_no_c0.shape != (TARGET_FRAMES, TARGET_MFCC_DIM):
            print(f"⚠️  尺寸不匹配: 期望({TARGET_FRAMES}, {TARGET_MFCC_DIM}), 实际{mfcc_no_c0.shape}")
            return None

        print(f"✅ MFCC提取完成:")
        print(f"   最终形状: {mfcc_no_c0.shape}")
        print(f"   最终范围: [{mfcc_no_c0.min():.6f}, {mfcc_no_c0.max():.6f}]")
        print(f"   总元素数: {mfcc_no_c0.size}")

        return mfcc_no_c0.astype(np.float32)

    except Exception as e:
        print(f"❌ MFCC特征提取失败: {e}")
        return None

def generate_c_header(mfcc_features, output_path, audio_filename):
    """
    生成C语言头文件
    
    Args:
        mfcc_features: MFCC特征数组 [198, 12]
        output_path: 输出文件路径
        audio_filename: 原始音频文件名
    """
    print(f"📝 生成C头文件: {output_path}")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 展平数组为一维
    mfcc_flat = mfcc_features.flatten()  # 2376个元素
    
    # 生成C代码
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(f"""/*
 * 真实MFCC特征数据 - 从音频文件提取
 * 
 * 源音频: {audio_filename}
 * 生成时间: {np.datetime64('now')}
 * 
 * 特征参数:
 * - 采样率: {SAMPLE_RATE}Hz
 * - 音频长度: 2.0秒
 * - MFCC维度: {TARGET_MFCC_DIM} (去除C0)
 * - 时间帧数: {TARGET_FRAMES}
 * - 总元素数: {len(mfcc_flat)}
 * 
 * 数据范围: [{mfcc_features.min():.6f}, {mfcc_features.max():.6f}]
 * 数据均值: {mfcc_features.mean():.6f}
 * 数据标准差: {mfcc_features.std():.6f}
 */

#ifndef REAL_MFCC_DATA_H
#define REAL_MFCC_DATA_H

#include <stdint.h>

// MFCC特征配置
#define REAL_MFCC_FRAMES    {TARGET_FRAMES}
#define REAL_MFCC_DIM       {TARGET_MFCC_DIM}
#define REAL_MFCC_SIZE      (REAL_MFCC_FRAMES * REAL_MFCC_DIM)

// 真实MFCC特征数据 (float格式)
static const float real_mfcc_data[REAL_MFCC_SIZE] = {{
""")
        
        # 写入数据，每行8个元素
        for i, value in enumerate(mfcc_flat):
            if i % 8 == 0:
                f.write("    ")
            
            f.write(f"{value:12.6f}f")
            
            if i < len(mfcc_flat) - 1:
                f.write(", ")
            
            if (i + 1) % 8 == 0:
                f.write("\n")
        
        if len(mfcc_flat) % 8 != 0:
            f.write("\n")
        
        f.write("};\n\n")

        # 写入统计信息
        f.write(f"// 统计信息\n")
        f.write(f"#define REAL_MFCC_MIN       {mfcc_features.min():.6f}f\n")
        f.write(f"#define REAL_MFCC_MAX       {mfcc_features.max():.6f}f\n")
        f.write(f"#define REAL_MFCC_MEAN      {mfcc_features.mean():.6f}f\n")
        f.write(f"#define REAL_MFCC_STD       {mfcc_features.std():.6f}f\n")
        f.write(f"\n#endif // REAL_MFCC_DATA_H\n")
    
    print(f"✅ C头文件生成完成:")
    print(f"   文件大小: {os.path.getsize(output_path)} 字节")
    print(f"   数据元素: {len(mfcc_flat)} 个float")

def visualize_mfcc(mfcc_features, save_path=None):
    """
    可视化MFCC特征

    Args:
        mfcc_features: MFCC特征数组 [198, 12]
        save_path: 保存图片的路径 (可选)
    """
    print(f"📊 生成MFCC可视化图...")

    try:
        plt.figure(figsize=(12, 8))

        # 转置为 [mfcc_dim, frames] 用于显示
        mfcc_display = mfcc_features.T

        # 简单的热力图显示
        plt.imshow(mfcc_display, aspect='auto', origin='lower', cmap='coolwarm')
        plt.colorbar(label='MFCC值')
        plt.title(f'MFCC特征 (去除C0)\n形状: {mfcc_features.shape}, 范围: [{mfcc_features.min():.2f}, {mfcc_features.max():.2f}]')
        plt.xlabel('时间帧')
        plt.ylabel('MFCC系数')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"   可视化图保存: {save_path}")

        plt.close()  # 关闭图形，避免显示

    except Exception as e:
        print(f"⚠️  可视化生成失败: {e}")
        print("   跳过可视化步骤")

def main():
    """主函数"""
    print("🎙️  MFCC特征提取工具")
    print("=" * 50)

    # 获取脚本所在目录
    script_dir = Path(__file__).parent

    # 输入音频文件路径 (相对于脚本目录)
    audio_path = script_dir / "test" / "xiaoxin.wav"

    print(f"🔍 查找音频文件: {audio_path}")

    # 检查文件是否存在
    if not audio_path.exists():
        print(f"❌ 音频文件不存在: {audio_path}")
        print(f"   脚本目录: {script_dir}")
        print(f"   当前工作目录: {os.getcwd()}")
        return
    
    # 提取MFCC特征
    mfcc_features = extract_mfcc_from_audio(str(audio_path))
    if mfcc_features is None:
        return

    # 创建输出目录 (相对于脚本目录)
    output_dir = script_dir / "mfcc_data"
    output_dir.mkdir(exist_ok=True)

    # 生成C头文件
    audio_filename = audio_path.name
    output_path = output_dir / "real_mfcc_data.h"
    generate_c_header(mfcc_features, str(output_path), audio_filename)

    # 生成可视化图
    viz_path = output_dir / "mfcc_visualization.png"
    visualize_mfcc(mfcc_features, str(viz_path))

    print(f"\n🎯 处理完成!")
    print(f"   输入音频: {audio_path}")
    print(f"   输出目录: {output_dir}/")
    print(f"   C头文件: {output_path}")
    print(f"   可视化图: {viz_path}")

if __name__ == "__main__":
    main()
