<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_WZ198HInEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_WZ198XInEfCAZJ6UDgwwcw" bindingContexts="_WZ3Nf3InEfCAZJ6UDgwwcw">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.c&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_inference.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_output.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_tensor.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_softmax.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_dense.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_flatten.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_avgpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_activation.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_maxpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_input.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;qemu/galaxy_sdk/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/inc/nnom.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/inc/nnom.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pdm_audio_02_data.h&quot; tooltip=&quot;qemu/galaxy_sdk/pdm_audio_02_data.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/pdm_audio_02_data.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pdm_audio_02_data.c&quot; tooltip=&quot;qemu/galaxy_sdk/pdm_audio_02_data.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/pdm_audio_02_data.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_weights.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_weights.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_weights.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;real_mfcc_data.h&quot; tooltip=&quot;qemu/galaxy_sdk/real_mfcc_data.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/real_mfcc_data.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_inference.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_weights1.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_weights1.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_weights1.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_port.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/port/nnom_port.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/port/nnom_port.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/inc/layers/nnom_conv2d.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/inc/layers/nnom_conv2d.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;_stdint.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\sys\_stdint.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\sys\_stdint.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;_default_types.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\machine\_default_types.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\machine\_default_types.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stddef.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\lib\gcc\riscv64-unknown-elf\13.1.1\include\stddef.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\lib\gcc\riscv64-unknown-elf\13.1.1\include\stddef.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local_q15.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_utils.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_utils.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_utils.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_WZ198XInEfCAZJ6UDgwwcw" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_WZ198nInEfCAZJ6UDgwwcw" label="%trimmedwindow.label.eclipseSDK" x="2246" y="94" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1754150091289"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_WZ198nInEfCAZJ6UDgwwcw" selectedElement="_WZ1983InEfCAZJ6UDgwwcw" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_WZ1983InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_WZ1-CXInEfCAZJ6UDgwwcw">
        <children xsi:type="advanced:Perspective" xmi:id="_WZ199HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_WZ199XInEfCAZJ6UDgwwcw" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewMakeFromExisting</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:myplugintest.wizards.SampleNewWizard</tags>
          <tags>persp.newWizSC:myplugintest.wizards.PackageWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.profileActionSet</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_WZ199XInEfCAZJ6UDgwwcw" selectedElement="_WZ19-nInEfCAZJ6UDgwwcw" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_WZ199nInEfCAZJ6UDgwwcw" elementId="topLeft" containerData="1417" selectedElement="_WZ1993InEfCAZJ6UDgwwcw">
              <children xsi:type="advanced:Placeholder" xmi:id="_WZ1993InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_WZ2mW3InEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_WZ19-HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_WZ2md3InEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_WZ19-XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_WZ2meHInEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_WZ19-nInEfCAZJ6UDgwwcw" containerData="8583" selectedElement="_WZ1-A3InEfCAZJ6UDgwwcw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_WZ19-3InEfCAZJ6UDgwwcw" containerData="6809" selectedElement="_WZ19_HInEfCAZJ6UDgwwcw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ19_HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_WZ2mIXInEfCAZJ6UDgwwcw"/>
                <children xsi:type="basic:PartStack" xmi:id="_WZ19_XInEfCAZJ6UDgwwcw" elementId="topRight" containerData="2500" selectedElement="_WZ19_nInEfCAZJ6UDgwwcw">
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ19_nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" ref="_WZ2mrXInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ19_3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_WZ2msHInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-AHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_WZ2msXInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Mylyn</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-AXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_WZ2msnInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-AnInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" ref="_WZ2mtHInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:CMSIS Packs</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_WZ1-A3InEfCAZJ6UDgwwcw" elementId="bottom" containerData="3191" selectedElement="_WZ1-BnInEfCAZJ6UDgwwcw">
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-BHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" ref="_WZ2meXInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-BXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" ref="_WZ2mgXInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-BnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" ref="_WZ2mgnInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-B3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" ref="_WZ2mrHInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-CHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_WZ2ms3InEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_WZ1-CXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_WZ1-CnInEfCAZJ6UDgwwcw" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.visualizer.view</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.xml.ui.perspective</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.SignalsView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ModuleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.executablesView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.debuggerConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.debugsources.view</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_WZ1-CnInEfCAZJ6UDgwwcw" selectedElement="_WZ1-EXInEfCAZJ6UDgwwcw" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_WZ1-C3InEfCAZJ6UDgwwcw" containerData="1453" selectedElement="_WZ1-DHInEfCAZJ6UDgwwcw" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_WZ1-DHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="5000" selectedElement="_WZ1-DnInEfCAZJ6UDgwwcw">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-DXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" ref="_WZ2mtXInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-DnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_WZ2mW3InEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_WZ1-D3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.viewMStack" toBeRendered="false" containerData="5000">
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-EHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" toBeRendered="false" ref="_WZ2nPnInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_WZ1-EXInEfCAZJ6UDgwwcw" containerData="8547" selectedElement="_WZ1-EnInEfCAZJ6UDgwwcw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_WZ1-EnInEfCAZJ6UDgwwcw" containerData="6383" selectedElement="_WZ1-E3InEfCAZJ6UDgwwcw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-E3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" containerData="6500" ref="_WZ2mIXInEfCAZJ6UDgwwcw"/>
                <children xsi:type="basic:PartStack" xmi:id="_WZ1-FHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="3500" selectedElement="_WZ1-FXInEfCAZJ6UDgwwcw">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-FXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" ref="_WZ2m3XInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-FnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" ref="_WZ2m_XInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-F3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" ref="_WZ2nFnInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-GHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_WZ2mrXInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-GXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_WZ2mrHInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-GnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_WZ2msHInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-G3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" ref="_WZ2nMXInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-HHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" toBeRendered="false" ref="_WZ2nM3InEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-HXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" toBeRendered="false" ref="_WZ2nNHInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-HnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" ref="_WZ2nOHInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-H3InEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" ref="_WZ2nSXInEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_WZ1-IHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="3617" selectedElement="_WZ1-IXInEfCAZJ6UDgwwcw">
                <tags>Debug</tags>
                <tags>General</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-IXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" ref="_WZ2mgnInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-InInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_WZ2m2XInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-I3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_WZ2meHInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-JHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_WZ2m3HInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-JXInEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_WZ2nMnInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-JnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" ref="_WZ2meXInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-J3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" ref="_WZ2nNXInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-KHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_WZ2ms3InEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-KXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" ref="_WZ2nP3InEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-KnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" toBeRendered="false" ref="_WZ2nSHInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-K3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" ref="_WZ2nWnInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-LHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView:PIN_CLONE_VIEW_1" toBeRendered="false" ref="_WZ2nb3InEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-LXInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" ref="_WZ2ncnInEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
          </children>
          <windows xsi:type="basic:TrimmedWindow" xmi:id="_WZ1-LnInEfCAZJ6UDgwwcw" toBeRendered="false" x="1805" y="140" width="1648" height="361">
            <tags>shellMaximized</tags>
            <children xsi:type="basic:PartStack" xmi:id="_WZ1-L3InEfCAZJ6UDgwwcw" elementId="PartStack@523685f6" toBeRendered="false">
              <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-MHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" toBeRendered="false" ref="_WZ2nO3InEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Debug</tags>
              </children>
            </children>
          </windows>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_WZ1-MXInEfCAZJ6UDgwwcw" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-MnInEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_WZ2mHHInEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-M3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_WZ2mHXInEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_WZ1-NHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_WZ2mIHInEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2mHHInEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2mHXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_WZ2mHnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2mH3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2mIHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_WZ2mIXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" selectedElement="_WZ2mInInEfCAZJ6UDgwwcw">
      <children xsi:type="basic:PartStack" xmi:id="_WZ2mInInEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_WZ2mJ3InEfCAZJ6UDgwwcw">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
        <tags>active</tags>
        <tags>noFocus</tags>
        <children xsi:type="basic:Part" xmi:id="_WZ2mI3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; partName=&quot;main.c&quot; title=&quot;main.c&quot; tooltip=&quot;qemu/galaxy_sdk/main.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/main.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3300&quot; selectionTopPixel=&quot;2090&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mJ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="speaker_inference.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.c&quot; partName=&quot;speaker_inference.c&quot; title=&quot;speaker_inference.c&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/speaker_inference.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;330&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>active</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mK3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.c&quot; partName=&quot;nnom.c&quot; title=&quot;nnom.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;28295&quot; selectionTopPixel=&quot;22836&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mL3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_input.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_input.c&quot; partName=&quot;nnom_input.c&quot; title=&quot;nnom_input.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4507&quot; selectionTopPixel=&quot;2662&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mM3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_tensor.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_tensor.c&quot; partName=&quot;nnom_tensor.c&quot; title=&quot;nnom_tensor.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;573&quot; selectionTopPixel=&quot;330&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mN3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_conv2d.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.c&quot; partName=&quot;nnom_conv2d.c&quot; title=&quot;nnom_conv2d.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;18880&quot; selectionTopPixel=&quot;9020&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mO3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_local.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local.c&quot; partName=&quot;nnom_local.c&quot; title=&quot;nnom_local.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;51325&quot; selectionTopPixel=&quot;29458&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mP3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_activation.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_activation.c&quot; partName=&quot;nnom_activation.c&quot; title=&quot;nnom_activation.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3598&quot; selectionTopPixel=&quot;3058&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mQ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_maxpool.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_maxpool.c&quot; partName=&quot;nnom_maxpool.c&quot; title=&quot;nnom_maxpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;5121&quot; selectionTopPixel=&quot;3674&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mR3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_avgpool.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_avgpool.c&quot; partName=&quot;nnom_avgpool.c&quot; title=&quot;nnom_avgpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4218&quot; selectionTopPixel=&quot;3121&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mS3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_flatten.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_flatten.c&quot; partName=&quot;nnom_flatten.c&quot; title=&quot;nnom_flatten.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2137&quot; selectionTopPixel=&quot;1317&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mT3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_dense.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_dense.c&quot; partName=&quot;nnom_dense.c&quot; title=&quot;nnom_dense.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;7044&quot; selectionTopPixel=&quot;4287&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mU3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_softmax.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_softmax.c&quot; partName=&quot;nnom_softmax.c&quot; title=&quot;nnom_softmax.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2129&quot; selectionTopPixel=&quot;1364&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WZ2mV3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_output.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_output.c&quot; partName=&quot;nnom_output.c&quot; title=&quot;nnom_output.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1263&quot; selectionTopPixel=&quot;704&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2mW3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; currentWorkingSetName=&quot;Aggregate for window 1754150091289&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_WZ2mXHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2mcHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2md3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2meHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2meXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;expanded>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Warnings (2 items)&quot;/>&#xD;&#xA;&lt;/expanded>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_WZ2menInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2mfHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2mgXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2mgnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_WZ2mg3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2mh3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2mrHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2mrXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_WZ2mrnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2mr3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2msHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2msXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2msnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2ms3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2mtHInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
      <tags>View</tags>
      <tags>categoryTag:CMSIS Packs</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2mtXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2mtnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2mwXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2m2XInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2m2nInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2m23InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2m3HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2m3XInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2m3nInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2m8nInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2m_XInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2m_nInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2nCHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nFnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2nF3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2nIXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nMXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Visualizer" iconURI="platform:/plugin/org.eclipse.cdt.visualizer.ui/icons/full/view16/visualizer_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.visualizer.ui.VisualizerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.visualizer.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nMnInEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nM3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nNHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nNXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2nNnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2nN3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nOHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view disassembly.syncActiveContext=&quot;true&quot; disassembly.trackExpression=&quot;false&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2nOXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2nOnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nO3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2nPHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2nPXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nPnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nP3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2nQHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2nQnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nSHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nSXInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2nSnInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2nVHInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nWnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2nW3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2nYnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2nb3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView:PIN_CLONE_VIEW_1" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables &lt;1>" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_WZ2ncHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2ncXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZ2ncnInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view isPinned=&quot;false&quot;>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.search.text.FileSearchResultPage&quot; org.eclipse.search.lastActivation=&quot;1&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; org.eclipse.search.resultpage.limit=&quot;1000&quot; org.eclipse.search.resultpage.sorting=&quot;2&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_WZ2nc3InEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WZ2nhHInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_WZ2nq3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolControl" xmi:id="_WZ2nrHInEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar" contributionURI="bundleclass://org.eclipse.launchbar.ui.controls/org.eclipse.launchbar.ui.controls.internal.LaunchBarControl"/>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2nrXInEfCAZJ6UDgwwcw" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_WZ2nrnInEfCAZJ6UDgwwcw" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2nr3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_WZ2nt3InEfCAZJ6UDgwwcw" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_WZ5Bw3InEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2nvXInEfCAZJ6UDgwwcw" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_WZ2nvnInEfCAZJ6UDgwwcw" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2nv3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_WZ2nwXInEfCAZJ6UDgwwcw" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_WZ4cw3InEfCAZJ6UDgwwcw"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_WZ2nwnInEfCAZJ6UDgwwcw" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_WZ4dOHInEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2nw3InEfCAZJ6UDgwwcw" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_WZ2nxHInEfCAZJ6UDgwwcw" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2oB3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2oCHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2oDXInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2oEnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2oGHInEfCAZJ6UDgwwcw" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_WZ2oGXInEfCAZJ6UDgwwcw" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2oGnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_WZ2oIHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_WZ5BdnInEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2oJXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.editor.CEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2oJnInEfCAZJ6UDgwwcw" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_WZ2oJ3InEfCAZJ6UDgwwcw" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2oKHInEfCAZJ6UDgwwcw" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_WZ2oKXInEfCAZJ6UDgwwcw" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WZ2oKnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_WZ2oLXInEfCAZJ6UDgwwcw" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_WZ2oMXInEfCAZJ6UDgwwcw" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_WZ2oOHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_WZ2oOXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_WZ2oOnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_WZ2oO3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_WZ2oQ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_WZ2oRHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_WZ2oRXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_WZ2oRnInEfCAZJ6UDgwwcw" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_WZ3Nf3InEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ2oR3InEfCAZJ6UDgwwcw" keySequence="CTRL+1" command="_WZ4ciXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oSHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+L" command="_WZ5B-HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oSXInEfCAZJ6UDgwwcw" keySequence="CTRL+V" command="_WZ4b9XInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oSnInEfCAZJ6UDgwwcw" keySequence="CTRL+A" command="_WZ4dHXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oS3InEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_WZ4dZnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oTHInEfCAZJ6UDgwwcw" keySequence="CTRL+X" command="_WZ4cy3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oTXInEfCAZJ6UDgwwcw" keySequence="CTRL+Y" command="_WZ4dOHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oTnInEfCAZJ6UDgwwcw" keySequence="CTRL+Z" command="_WZ4cw3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oT3InEfCAZJ6UDgwwcw" keySequence="ALT+PAGE_UP" command="_WZ4dQnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oUHInEfCAZJ6UDgwwcw" keySequence="ALT+PAGE_DOWN" command="_WZ5BRXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oUXInEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_WZ4b9XInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oUnInEfCAZJ6UDgwwcw" keySequence="ALT+F11" command="_WZ4cMXInEfCAZJ6UDgwwcw">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_WZ2oU3InEfCAZJ6UDgwwcw" keySequence="CTRL+F10" command="_WZ4cFnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oVHInEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_WZ4dZnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oVXInEfCAZJ6UDgwwcw" keySequence="CTRL+PAGE_UP" command="_WZ5B2HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oVnInEfCAZJ6UDgwwcw" keySequence="CTRL+PAGE_DOWN" command="_WZ4ckXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oV3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+F3" command="_WZ5BzXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oWHInEfCAZJ6UDgwwcw" keySequence="SHIFT+DEL" command="_WZ4cy3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oWXInEfCAZJ6UDgwwcw" keySequence="ALT+/" command="_WZ5BnXInEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_WZ2oWnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.textEditorScope" bindingContext="_WZ3NhHInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ2oW3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+CR" command="_WZ5BzHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oXHInEfCAZJ6UDgwwcw" keySequence="CTRL+BS" command="_WZ4bznInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oXXInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+Q" command="_WZ4cdnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oXnInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+C" command="_WZ4cCnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oX3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+J" command="_WZ4cZ3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oYHInEfCAZJ6UDgwwcw" keySequence="CTRL++" command="_WZ4dzXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oYXInEfCAZJ6UDgwwcw" keySequence="CTRL+-" command="_WZ4dBnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oYnInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_WZ5BZnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oY3InEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+V" command="_WZ4cIXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oZHInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+J" command="_WZ4cgnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oZXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+A" command="_WZ4dhHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oZnInEfCAZJ6UDgwwcw" keySequence="CTRL+J" command="_WZ4cHHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oZ3InEfCAZJ6UDgwwcw" keySequence="CTRL+L" command="_WZ5BsHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oaHInEfCAZJ6UDgwwcw" keySequence="CTRL+D" command="_WZ4cJXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ2oaXInEfCAZJ6UDgwwcw" keySequence="CTRL+=" command="_WZ4dzXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MEHInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+/" command="_WZ5CCXInEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_WZ3MEXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Y" command="_WZ4bxXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MEnInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+DEL" command="_WZ5BonInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3ME3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+X" command="_WZ4dbnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MFHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+Y" command="_WZ4dBHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MFXInEfCAZJ6UDgwwcw" keySequence="CTRL+DEL" command="_WZ4cv3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MFnInEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_WZ5CVnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MF3InEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_DOWN" command="_WZ5BUHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MGHInEfCAZJ6UDgwwcw" keySequence="SHIFT+END" command="_WZ4dDHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MGXInEfCAZJ6UDgwwcw" keySequence="SHIFT+HOME" command="_WZ4c83InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MGnInEfCAZJ6UDgwwcw" keySequence="END" command="_WZ5B5HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MG3InEfCAZJ6UDgwwcw" keySequence="INSERT" command="_WZ4dpXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MHHInEfCAZJ6UDgwwcw" keySequence="F2" command="_WZ4ck3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MHXInEfCAZJ6UDgwwcw" keySequence="HOME" command="_WZ5CBHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MHnInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_UP" command="_WZ5CMHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MH3InEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_DOWN" command="_WZ4dIHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MIHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+INSERT" command="_WZ4cUnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MIXInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_WZ4dDnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MInInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_WZ4cWXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MI3InEfCAZJ6UDgwwcw" keySequence="CTRL+F10" command="_WZ5ByHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MJHInEfCAZJ6UDgwwcw" keySequence="CTRL+END" command="_WZ5BUnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MJXInEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_UP" command="_WZ4cQXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MJnInEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_DOWN" command="_WZ5CZnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MJ3InEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_LEFT" command="_WZ4dYHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MKHInEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_RIGHT" command="_WZ4cc3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MKXInEfCAZJ6UDgwwcw" keySequence="CTRL+HOME" command="_WZ4b9HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MKnInEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_MULTIPLY" command="_WZ5BZXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MK3InEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_ADD" command="_WZ5CH3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MLHInEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_SUBTRACT" command="_WZ5BynInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MLXInEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_DIVIDE" command="_WZ4cQ3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MLnInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_WZ5BbnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3ML3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_WZ4dpnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MMHInEfCAZJ6UDgwwcw" keySequence="SHIFT+CR" command="_WZ5CAXInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3MMXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_WZ3NmXInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3MMnInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+C" command="_WZ5CaXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MM3InEfCAZJ6UDgwwcw" keySequence="CTRL+TAB" command="_WZ5CYnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MNHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_WZ4c6HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MNXInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+T" command="_WZ4cpHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MNnInEfCAZJ6UDgwwcw" keySequence="CTRL+7" command="_WZ4b-HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MN3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+H" command="_WZ4duHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MOHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+N" command="_WZ4ccnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MOXInEfCAZJ6UDgwwcw" keySequence="CTRL+/" command="_WZ4b-HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MOnInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+O" command="_WZ4c3HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MO3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+A" command="_WZ5BznInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MPHInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+S" command="_WZ5CRHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MPXInEfCAZJ6UDgwwcw" keySequence="CTRL+#" command="_WZ5BhHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MPnInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_WZ4b-HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MP3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F" command="_WZ5CY3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MQHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_WZ4byXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MQXInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+H" command="_WZ4cjHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MQnInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+I" command="_WZ4dkXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MQ3InEfCAZJ6UDgwwcw" keySequence="CTRL+T" command="_WZ5BVXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MRHInEfCAZJ6UDgwwcw" keySequence="CTRL+I" command="_WZ4cZHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MRXInEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_WZ4dd3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MRnInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+/" command="_WZ5CHHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MR3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_WZ5Bp3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MSHInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+S" command="_WZ4cGHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MSXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+T" command="_WZ5BinInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MSnInEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_WZ5CO3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MS3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+L" command="_WZ4dInInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MTHInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+M" command="_WZ4b63InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MTXInEfCAZJ6UDgwwcw" keySequence="CTRL+=" command="_WZ5BhHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MTnInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+O" command="_WZ4c0XInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MT3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Z" command="_WZ5Bu3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MUHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+\" command="_WZ4djXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MUXInEfCAZJ6UDgwwcw" keySequence="F3" command="_WZ5CbnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MUnInEfCAZJ6UDgwwcw" keySequence="F4" command="_WZ5CF3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MU3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_UP" command="_WZ5BrHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MVHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_WZ5BSHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MVXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_WZ4c-XInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MVnInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_WZ5CZHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MV3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_WZ5BnnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MWHInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_WZ5B83InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MWXInEfCAZJ6UDgwwcw" keySequence="ALT+C" command="_WZ4djnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MWnInEfCAZJ6UDgwwcw" keySequence="SHIFT+TAB" command="_WZ4duXInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3MW3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_WZ3NpXInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3MXHInEfCAZJ6UDgwwcw" keySequence="CTRL+CR" command="_WZ4csnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MXXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+C" command="_WZ4dU3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MXnInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_WZ4dDXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MX3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+U" command="_WZ5BXXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MYHInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+I" command="_WZ4dB3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MYXInEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_WZ4d2nInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MYnInEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_DOWN" command="_WZ4ct3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MY3InEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_WZ4cNnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MZHInEfCAZJ6UDgwwcw" keySequence="INSERT" command="_WZ4dAnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MZXInEfCAZJ6UDgwwcw" keySequence="F4" command="_WZ4cEnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MZnInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_WZ5BjHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MZ3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_WZ4dCXInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3MaHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.window" bindingContext="_WZ3NgHInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3MaXInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+T" command="_WZ4cFXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3ManInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+L" command="_WZ4dmHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Ma3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q O" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MbHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_WZ3MbXInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+B" command="_WZ4d5XInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MbnInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+R" command="_WZ5Ca3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mb3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Q" command="_WZ4d23InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3McHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+S" command="_WZ4dx3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3McXInEfCAZJ6UDgwwcw" keySequence="CTRL+3" command="_WZ4cknInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3McnInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q S" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3Mc3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_WZ3MdHInEfCAZJ6UDgwwcw" keySequence="CTRL+6" command="_WZ5CAHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MdXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q V" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MdnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_WZ3Md3InEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+G" command="_WZ4d03InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MeHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+W" command="_WZ4cynInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MeXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q H" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MenInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_WZ3Me3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+K" command="_WZ4cPXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MfHInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q K" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MfXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_WZ3MfnInEfCAZJ6UDgwwcw" keySequence="CTRL+," command="_WZ4b-XInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mf3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q L" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MgHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_WZ3MgXInEfCAZJ6UDgwwcw" keySequence="CTRL+." command="_WZ5CMnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MgnInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_WZ4c9HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mg3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+B" command="_WZ4cPnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MhHInEfCAZJ6UDgwwcw" keySequence="CTRL+#" command="_WZ4cF3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MhXInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+T" command="_WZ4dYnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MhnInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+E" command="_WZ4cTnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mh3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q X" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MiHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_WZ3MiXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Y" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MinInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_WZ3Mi3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Z" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MjHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_WZ3MjXInEfCAZJ6UDgwwcw" keySequence="CTRL+P" command="_WZ5Bw3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MjnInEfCAZJ6UDgwwcw" keySequence="CTRL+Q" command="_WZ5B03InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mj3InEfCAZJ6UDgwwcw" keySequence="CTRL+S" command="_WZ4dCHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MkHInEfCAZJ6UDgwwcw" keySequence="CTRL+W" command="_WZ4dPXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MkXInEfCAZJ6UDgwwcw" keySequence="CTRL+H" command="_WZ5BnHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MknInEfCAZJ6UDgwwcw" keySequence="CTRL+K" command="_WZ5BQnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mk3InEfCAZJ6UDgwwcw" keySequence="CTRL+M" command="_WZ5Bl3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MlHInEfCAZJ6UDgwwcw" keySequence="CTRL+N" command="_WZ5CRnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MlXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+?" command="_WZ4cpXInEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_WZ3MlnInEfCAZJ6UDgwwcw" keySequence="CTRL+B" command="_WZ4b_nInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Ml3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q B" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MmHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_WZ3MmXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q C" command="_WZ4d23InEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MmnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_WZ3Mm3InEfCAZJ6UDgwwcw" keySequence="CTRL+E" command="_WZ4cvHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MnHInEfCAZJ6UDgwwcw" keySequence="CTRL+F" command="_WZ4cLHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MnXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+W" command="_WZ5CL3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MnnInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+H" command="_WZ4ctHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mn3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+N" command="_WZ4cx3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MoHInEfCAZJ6UDgwwcw" keySequence="CTRL+_" command="_WZ4cqHInEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3MoXInEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_WZ3MonInEfCAZJ6UDgwwcw" keySequence="CTRL+{" command="_WZ4cqHInEfCAZJ6UDgwwcw">
      <parameters xmi:id="_WZ3Mo3InEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_WZ3MpHInEfCAZJ6UDgwwcw" keySequence="SHIFT+F9" command="_WZ4c23InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MpXInEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_LEFT" command="_WZ4cGnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MpnInEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_WZ4c3nInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mp3InEfCAZJ6UDgwwcw" keySequence="SHIFT+F5" command="_WZ4dJ3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MqHInEfCAZJ6UDgwwcw" keySequence="ALT+F7" command="_WZ4di3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MqXInEfCAZJ6UDgwwcw" keySequence="F9" command="_WZ4c63InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MqnInEfCAZJ6UDgwwcw" keySequence="F11" command="_WZ5CE3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mq3InEfCAZJ6UDgwwcw" keySequence="F12" command="_WZ5BoHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MrHInEfCAZJ6UDgwwcw" keySequence="F2" command="_WZ4b_XInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MrXInEfCAZJ6UDgwwcw" keySequence="F5" command="_WZ4c5XInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MrnInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F7" command="_WZ5CFXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mr3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F8" command="_WZ4cp3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MsHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F9" command="_WZ4dF3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MsXInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_LEFT" command="_WZ5B03InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MsnInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_RIGHT" command="_WZ4cRnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Ms3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F12" command="_WZ4b3nInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MtHInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F4" command="_WZ4cynInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MtXInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F6" command="_WZ4dzHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MtnInEfCAZJ6UDgwwcw" keySequence="CTRL+F7" command="_WZ4dZ3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mt3InEfCAZJ6UDgwwcw" keySequence="CTRL+F8" command="_WZ4cinInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MuHInEfCAZJ6UDgwwcw" keySequence="CTRL+F9" command="_WZ4cUXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MuXInEfCAZJ6UDgwwcw" keySequence="CTRL+F11" command="_WZ5B53InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MunInEfCAZJ6UDgwwcw" keySequence="CTRL+F12" command="_WZ4cQHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mu3InEfCAZJ6UDgwwcw" keySequence="CTRL+F4" command="_WZ4dPXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MvHInEfCAZJ6UDgwwcw" keySequence="CTRL+F6" command="_WZ4cNHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MvXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+F7" command="_WZ5BVHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MvnInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_WZ4ds3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mv3InEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_WZ5CUnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MwHInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_WZ4doXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MwXInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_WZ4dxnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MwnInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_WZ4cqnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mw3InEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+F12" command="_WZ5CIXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MxHInEfCAZJ6UDgwwcw" keySequence="DEL" command="_WZ4cOXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MxXInEfCAZJ6UDgwwcw" keySequence="ALT+?" command="_WZ4cpXInEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_WZ3MxnInEfCAZJ6UDgwwcw" keySequence="ALT+-" command="_WZ4denInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mx3InEfCAZJ6UDgwwcw" keySequence="ALT+CR" command="_WZ5Bh3InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3MyHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_WZ3Nh3InEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3MyXInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_WZ4c_3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MynInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_WZ5B43InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3My3InEfCAZJ6UDgwwcw" keySequence="F3" command="_WZ5B1HInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3MzHInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_WZ3NjXInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3MzXInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_WZ4dhXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3MznInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+A" command="_WZ5CZ3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3Mz3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_WZ5B_HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M0HInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F" command="_WZ5CQnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M0XInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+>" command="_WZ5BsnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M0nInEfCAZJ6UDgwwcw" keySequence="CTRL+I" command="_WZ5BxXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M03InEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_WZ4dk3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M1HInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+/" command="_WZ4deXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M1XInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+\" command="_WZ4d1HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M1nInEfCAZJ6UDgwwcw" keySequence="F3" command="_WZ4dgnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M13InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_UP" command="_WZ4cY3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M2HInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_WZ4dL3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M2XInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_WZ5BTHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M2nInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_WZ5BYXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M23InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_WZ4cDHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M3HInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_WZ4dunInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3M3XInEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareEditorScope" bindingContext="_WZ3NnHInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3M3nInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+C" command="_WZ4cCnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M33InEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_WZ5BZnInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3M4HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_WZ3NpnInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3M4XInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+T" command="_WZ4cpHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M4nInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+H" command="_WZ4duHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M43InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_WZ4byXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M5HInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+H" command="_WZ4cjHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M5XInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+I" command="_WZ4dkXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M5nInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_WZ5Bp3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M53InEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_WZ5CO3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M6HInEfCAZJ6UDgwwcw" keySequence="F3" command="_WZ5CbnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M6XInEfCAZJ6UDgwwcw" keySequence="F4" command="_WZ5CF3InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3M6nInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_WZ3NgnInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3M63InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+V" command="_WZ4c_XInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M7HInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_WZ5BfHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M7XInEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_WZ4bzHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M7nInEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_WZ5CEXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M73InEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_WZ4c_XInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M8HInEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_WZ5BfHInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3M8XInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_WZ3NiHInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3M8nInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+M" command="_WZ4b6HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M83InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+C" command="_WZ4dU3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M9HInEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_WZ5CKHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M9XInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_WZ4dDXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M9nInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+S" command="_WZ4c0HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M93InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+U" command="_WZ5BXXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M-HInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+I" command="_WZ4dB3InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3M-XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_WZ3NiXInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3M-nInEfCAZJ6UDgwwcw" keySequence="CTRL+/" command="_WZ4d4HInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M-3InEfCAZJ6UDgwwcw" keySequence="F3" command="_WZ4dwHInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3M_HInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_WZ3NnnInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3M_XInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+M" command="_WZ4dG3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M_nInEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+N" command="_WZ5CInInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3M_3InEfCAZJ6UDgwwcw" keySequence="CTRL+T" command="_WZ4clnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NAHInEfCAZJ6UDgwwcw" keySequence="CTRL+W" command="_WZ4dq3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NAXInEfCAZJ6UDgwwcw" keySequence="CTRL+N" command="_WZ4d0HInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NAnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugging" bindingContext="_WZ3Nn3InEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NA3InEfCAZJ6UDgwwcw" keySequence="CTRL+R" command="_WZ4dbHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NBHInEfCAZJ6UDgwwcw" keySequence="F7" command="_WZ5CN3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NBXInEfCAZJ6UDgwwcw" keySequence="F8" command="_WZ4doHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NBnInEfCAZJ6UDgwwcw" keySequence="F5" command="_WZ4cCXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NB3InEfCAZJ6UDgwwcw" keySequence="F6" command="_WZ4dD3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NCHInEfCAZJ6UDgwwcw" keySequence="CTRL+F2" command="_WZ5Bo3InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NCXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_WZ3NoHInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NCnInEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+," command="_WZ5B1nInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NC3InEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+." command="_WZ5BlHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NDHInEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_WZ5BlXInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NDXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_WZ3NhXInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NDnInEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_WZ4dK3InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3ND3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_WZ3Ni3InEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NEHInEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_WZ4b73InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NEXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_WZ3Np3InEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NEnInEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_WZ4cmnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NE3InEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_LEFT" command="_WZ4cIHInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NFHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_WZ3No3InEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NFXInEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_WZ4cNXInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NFnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" bindingContext="_WZ3Nr3InEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NF3InEfCAZJ6UDgwwcw" keySequence="CTRL+D" command="_WZ4dh3InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NGHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_WZ3NoXInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NGXInEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_WZ5CQ3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NGnInEfCAZJ6UDgwwcw" keySequence="HOME" command="_WZ4clXInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NG3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.console" bindingContext="_WZ3NmnInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NHHInEfCAZJ6UDgwwcw" keySequence="CTRL+Z" command="_WZ5CK3InEfCAZJ6UDgwwcw">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NHXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_WZ3NonInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NHnInEfCAZJ6UDgwwcw" keySequence="SHIFT+F7" command="_WZ4dv3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NH3InEfCAZJ6UDgwwcw" keySequence="SHIFT+F8" command="_WZ5BknInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NIHInEfCAZJ6UDgwwcw" keySequence="SHIFT+F5" command="_WZ4dEHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NIXInEfCAZJ6UDgwwcw" keySequence="SHIFT+F6" command="_WZ4cAXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NInInEfCAZJ6UDgwwcw" keySequence="CTRL+F5" command="_WZ5CC3InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NI3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_WZ3NqnInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NJHInEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_LEFT" command="_WZ5BVnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NJXInEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_WZ5BanInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NJnInEfCAZJ6UDgwwcw" keySequence="F3" command="_WZ5CbnInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NJ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_WZ3NinInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NKHInEfCAZJ6UDgwwcw" keySequence="F1" command="_WZ4b1nInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NKXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_WZ3NqHInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NKnInEfCAZJ6UDgwwcw" keySequence="F2" command="_WZ4cO3InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NK3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_WZ3NhnInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NLHInEfCAZJ6UDgwwcw" keySequence="F3" command="_WZ5CbnInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NLXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" bindingContext="_WZ3NrHInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NLnInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_WZ4c33InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NL3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_WZ4b5nInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NMHInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_WZ4dVXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NMXInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_WZ4drnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NMnInEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+HOME" command="_WZ5CLnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NM3InEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+END" command="_WZ4cu3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NNHInEfCAZJ6UDgwwcw" keySequence="ALT+R" command="_WZ5Bm3InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NNXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_WZ3NnXInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NNnInEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_WZ4dxHInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NN3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" bindingContext="_WZ3NjHInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NOHInEfCAZJ6UDgwwcw" keySequence="ESC CTRL+F" command="_WZ4dm3InEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NOXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.context" bindingContext="_WZ3NrnInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NOnInEfCAZJ6UDgwwcw" keySequence="Z" command="_WZ4dI3InEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NO3InEfCAZJ6UDgwwcw" keySequence="+" command="_WZ4bynInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NPHInEfCAZJ6UDgwwcw" keySequence="-" command="_WZ4cDnInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NPXInEfCAZJ6UDgwwcw" keySequence="/" command="_WZ4canInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NPnInEfCAZJ6UDgwwcw" keySequence="S" command="_WZ4czXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NP3InEfCAZJ6UDgwwcw" keySequence="W" command="_WZ4dMHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NQHInEfCAZJ6UDgwwcw" keySequence="A" command="_WZ4cmHInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NQXInEfCAZJ6UDgwwcw" keySequence="D" command="_WZ4dNXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NQnInEfCAZJ6UDgwwcw" keySequence="=" command="_WZ4bynInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NQ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_WZ3NpHInEfCAZJ6UDgwwcw">
    <bindings xmi:id="_WZ3NRHInEfCAZJ6UDgwwcw" keySequence="ALT+Y" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NRXInEfCAZJ6UDgwwcw" keySequence="ALT+A" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NRnInEfCAZJ6UDgwwcw" keySequence="ALT+B" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NR3InEfCAZJ6UDgwwcw" keySequence="ALT+C" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NSHInEfCAZJ6UDgwwcw" keySequence="ALT+D" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NSXInEfCAZJ6UDgwwcw" keySequence="ALT+E" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NSnInEfCAZJ6UDgwwcw" keySequence="ALT+F" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NS3InEfCAZJ6UDgwwcw" keySequence="ALT+G" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NTHInEfCAZJ6UDgwwcw" keySequence="ALT+P" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NTXInEfCAZJ6UDgwwcw" keySequence="ALT+R" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NTnInEfCAZJ6UDgwwcw" keySequence="ALT+S" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NT3InEfCAZJ6UDgwwcw" keySequence="ALT+T" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NUHInEfCAZJ6UDgwwcw" keySequence="ALT+V" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NUXInEfCAZJ6UDgwwcw" keySequence="ALT+W" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NUnInEfCAZJ6UDgwwcw" keySequence="ALT+H" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NU3InEfCAZJ6UDgwwcw" keySequence="ALT+L" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_WZ3NVHInEfCAZJ6UDgwwcw" keySequence="ALT+N" command="_WZ4ddXInEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_WZ3NVXInEfCAZJ6UDgwwcw" bindingContext="_WZ3NsHInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NVnInEfCAZJ6UDgwwcw" bindingContext="_WZ3NsXInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NV3InEfCAZJ6UDgwwcw" bindingContext="_WZ3NsnInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NWHInEfCAZJ6UDgwwcw" bindingContext="_WZ3Ns3InEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NWXInEfCAZJ6UDgwwcw" bindingContext="_WZ3NtHInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NWnInEfCAZJ6UDgwwcw" bindingContext="_WZ3NtXInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NW3InEfCAZJ6UDgwwcw" bindingContext="_WZ3NtnInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NXHInEfCAZJ6UDgwwcw" bindingContext="_WZ3Nt3InEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NXXInEfCAZJ6UDgwwcw" bindingContext="_WZ3NuHInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NXnInEfCAZJ6UDgwwcw" bindingContext="_WZ3NuXInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NX3InEfCAZJ6UDgwwcw" bindingContext="_WZ3NunInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NYHInEfCAZJ6UDgwwcw" bindingContext="_WZ3Nu3InEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NYXInEfCAZJ6UDgwwcw" bindingContext="_WZ3NvHInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NYnInEfCAZJ6UDgwwcw" bindingContext="_WZ3NvXInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NY3InEfCAZJ6UDgwwcw" bindingContext="_WZ3NvnInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NZHInEfCAZJ6UDgwwcw" bindingContext="_WZ3Nv3InEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NZXInEfCAZJ6UDgwwcw" bindingContext="_WZ3NwHInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NZnInEfCAZJ6UDgwwcw" bindingContext="_WZ3NwXInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NZ3InEfCAZJ6UDgwwcw" bindingContext="_WZ3NwnInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NaHInEfCAZJ6UDgwwcw" bindingContext="_WZ3Nw3InEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NaXInEfCAZJ6UDgwwcw" bindingContext="_WZ3NxHInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NanInEfCAZJ6UDgwwcw" bindingContext="_WZ3NxXInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3Na3InEfCAZJ6UDgwwcw" bindingContext="_WZ3NxnInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NbHInEfCAZJ6UDgwwcw" bindingContext="_WZ3Nx3InEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NbXInEfCAZJ6UDgwwcw" bindingContext="_WZ3NyHInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NbnInEfCAZJ6UDgwwcw" bindingContext="_WZ3NyXInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3Nb3InEfCAZJ6UDgwwcw" bindingContext="_WZ3NynInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NcHInEfCAZJ6UDgwwcw" bindingContext="_WZ3Ny3InEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NcXInEfCAZJ6UDgwwcw" bindingContext="_WZ3NzHInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NcnInEfCAZJ6UDgwwcw" bindingContext="_WZ3NzXInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3Nc3InEfCAZJ6UDgwwcw" bindingContext="_WZ3NznInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NdHInEfCAZJ6UDgwwcw" bindingContext="_WZ3Nz3InEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NdXInEfCAZJ6UDgwwcw" bindingContext="_WZ3N0HInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NdnInEfCAZJ6UDgwwcw" bindingContext="_WZ3N0XInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3Nd3InEfCAZJ6UDgwwcw" bindingContext="_WZ3N0nInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NeHInEfCAZJ6UDgwwcw" bindingContext="_WZ3N03InEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NeXInEfCAZJ6UDgwwcw" bindingContext="_WZ3N1HInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NenInEfCAZJ6UDgwwcw" bindingContext="_WZ3N1XInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3Ne3InEfCAZJ6UDgwwcw" bindingContext="_WZ3N1nInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NfHInEfCAZJ6UDgwwcw" bindingContext="_WZ3N13InEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NfXInEfCAZJ6UDgwwcw" bindingContext="_WZ3N2HInEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_WZ3NfnInEfCAZJ6UDgwwcw" bindingContext="_WZ3N2XInEfCAZJ6UDgwwcw"/>
  <rootContext xmi:id="_WZ3Nf3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_WZ3NgHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_WZ3NgXInEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_WZ3NgnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_WZ3Ng3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_WZ3NhHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_WZ3NhXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_WZ3NhnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_WZ3Nh3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_WZ3NiHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_WZ3NiXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_WZ3NinInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_WZ3Ni3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
        </children>
        <children xmi:id="_WZ3NjHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" name="ChangeLog Editor"/>
        <children xmi:id="_WZ3NjXInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_WZ3NjnInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_WZ3Nj3InEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_WZ3NkHInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_WZ3NkXInEfCAZJ6UDgwwcw" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_WZ3NknInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_WZ3Nk3InEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_WZ3NlHInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_WZ3NlXInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_WZ3NlnInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_WZ3Nl3InEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_WZ3NmHInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
        </children>
        <children xmi:id="_WZ3NmXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_WZ3NmnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_WZ3Nm3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_WZ3NnHInEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_WZ3NnXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_WZ3NnnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_WZ3Nn3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_WZ3NoHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_WZ3NoXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_WZ3NonInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_WZ3No3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_WZ3NpHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_WZ3NpXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_WZ3NpnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_WZ3Np3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_WZ3NqHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_WZ3NqXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_WZ3NqnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_WZ3Nq3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_WZ3NrHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" name="UML2 Sequence Diagram Viewer"/>
  <rootContext xmi:id="_WZ3NrXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_WZ3NrnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.context" name="In Time-Based View"/>
  <rootContext xmi:id="_WZ3Nr3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" name="In Time Graph"/>
  <rootContext xmi:id="_WZ3NsHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_WZ3NsXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_WZ3NsnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_WZ3Ns3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_WZ3NtHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_WZ3NtXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_WZ3NtnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_WZ3Nt3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.actionSet" name="Auto::org.eclipse.mylyn.cdt.ui.actionSet"/>
  <rootContext xmi:id="_WZ3NuHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_WZ3NuXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_WZ3NunInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_WZ3Nu3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_WZ3NvHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_WZ3NvXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_WZ3NvnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_WZ3Nv3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_WZ3NwHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_WZ3NwXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_WZ3NwnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_WZ3Nw3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_WZ3NxHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_WZ3NxXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_WZ3NxnInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset" name="Auto::org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset"/>
  <rootContext xmi:id="_WZ3Nx3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_WZ3NyHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.doc.actionSet" name="Auto::org.eclipse.mylyn.doc.actionSet"/>
  <rootContext xmi:id="_WZ3NyXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_WZ3NynInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_WZ3Ny3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_WZ3NzHInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_WZ3NzXInEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_WZ3NznInEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_WZ3Nz3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_WZ3N0HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_WZ3N0XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_WZ3N0nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_WZ3N03InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_WZ3N1HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_WZ3N1XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_WZ3N1nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_WZ3N13InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_WZ3N2HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_WZ3N2XInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.debug.gdbjtag.qemu.riscv.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.riscvstudio.debug.gdbjtag.qemu.riscv.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_WZ3N2nInEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N23InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N3HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N3XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N3nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N33InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N4HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N4XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N4nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N43InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N5HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N5XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.testsrunner.resultsview" label="C/C++ Unit" iconURI="platform:/plugin/org.eclipse.cdt.testsrunner/icons/eview16/cppunit.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.testsrunner.internal.ui.view.ResultsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.testsrunner"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N5nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N53InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N6HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N6XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N6nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N63InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N7HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" label="Visualizer" iconURI="platform:/plugin/org.eclipse.cdt.visualizer.ui/icons/full/view16/visualizer_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.visualizer.ui.VisualizerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.visualizer.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N7XInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N7nInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N73InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N8HInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N8XInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N8nInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N83InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N9HInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N9XInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N9nInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N93InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N-HInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N-XInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N-nInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N-3InEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N_HInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView" label="Devices" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/hardware_chip.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N_XInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView" label="Boards" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/board.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N_nInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView" label="Keywords" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/info_obj.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3N_3InEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.PacksView" label="CMSIS Packs" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/packages.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.PacksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OAHInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView" label="Outline" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/outline_co.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OAXInEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OAnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" tooltip="" allowMultiple="true" category="Charts" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.dataviewers.charts.view.ChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.dataviewers.charts"/>
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OA3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gcov.view" label="gcov" iconURI="platform:/plugin/org.eclipse.linuxtools.gcov.core/icons/toggle.gif" tooltip="" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gcov.view.CovView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gcov.core"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OBHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gprof.view" label="gprof" iconURI="platform:/plugin/org.eclipse.linuxtools.gprof/icons/toggle.gif" tooltip="Gprof view displays the profiling information contained in a gmon.out file" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gprof.view.GmonView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gprof"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OBXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.ui.valgrindview" label="Valgrind" iconURI="platform:/plugin/org.eclipse.linuxtools.valgrind.ui/icons/valgrind-icon.png" tooltip="" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.valgrind.ui.ValgrindViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.valgrind.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OBnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.navigator.builds" label="Builds" iconURI="platform:/plugin/org.eclipse.mylyn.builds.ui/icons/eview16/build-view.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.builds.ui.view.BuildsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.builds.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OB3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.identity.ui.navigator.People" label="People" iconURI="platform:/plugin/org.eclipse.mylyn.commons.identity.ui/icons/obj16/people.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.identity.ui.PeopleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.identity.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OCHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OCXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.reviews.Explorer" label="Review" iconURI="platform:/plugin/org.eclipse.mylyn.reviews.ui/icons/obj16/review.png" tooltip="View artifacts and comments associated with reviews." category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.reviews.ui.views.ReviewExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.reviews.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OCnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OC3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3ODHInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3ODXInEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3ODnInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OD3InEfCAZJ6UDgwwcw" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OEHInEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OEXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OEnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OE3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.counters.ui.views.countersview" label="Counters" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.counters.ui/icons/counter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.counters.ui.views.CounterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.counters.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OFHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.analysis.graph.ui.criticalpath.view.criticalpathview" label="Critical Flow View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.graph.ui/icons/eview16/critical-path.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.graph.ui.criticalpath.view.CriticalPathView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.graph.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OFXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.lami.views.reportview" label="Analysis Report" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" allowMultiple="true" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.provisional.analysis.lami.ui.views.LamiReportView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.lami.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OFnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OF3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.scatter" label="Sched_Wakeup/Switch Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OGHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OGXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname" label="Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OGnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OG3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority" label="Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OHHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OHXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.density" label="Sched_Wakeup/Switch Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OHnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.controlflow" label="Control Flow" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/control_flow_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.controlflow.ControlFlowView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OH3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.resources" label="Resources" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.resources.ResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OIHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.cpuusage" label="CPU Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/cpu-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.cpuusage.CpuUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OIXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OInInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.scatter" label="System Call Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OI3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OJHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.density" label="System Call Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OJXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.kernelmemoryusageview" label="Kernel Memory Usage View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.kernelmemoryusage.KernelMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OJnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.diskioactivity" label="Disk I/O Activity" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.io.diskioactivity.DiskIOActivityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OJ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.callstack" label="Flame Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/callstack_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.profiling.ui.views.flamechart.FlameChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OKHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.callgraphDensity" label="Function Durations Distribution" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/funcdensity.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.CallGraphDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OKXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.flamegraph.flamegraphView" label="Flame Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/flame.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.flamegraph.FlameGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OKnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.statistics.callgraphstatistics" label="Function Duration Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.statistics.CallGraphStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OK3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table" label="Segment Store Table" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OLHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics" label="Descriptive Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OLXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2" label="Segments vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OLnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OL3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OMHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Matches Scatter Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OMXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.views.control" label="Control" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.control.ui/icons/eview16/control_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.control.ui.views.ControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.control.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OMnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.ust.memoryusage" label="UST Memory Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.ust.ui.views.memusage.UstMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OM3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3ONHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3ONXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.views.timegraph" label="XML Time Graph View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/ganttxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.timegraph.XmlTimeGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3ONnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.tmf.analysis.xml.ui.views.xyview" label="XML XY Chart View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/linechartxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.xychart.XmlXYView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3ON3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latencytable" label="Latency Table" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternLatencyTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OOHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.scattergraph" label="Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/scatter.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternScatterGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OOXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.density" label="Latency vs Count" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/density.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OOnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.statistics" label="Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OO3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.timechart" label="Time Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/timechart_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.timechart.TimeChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OPHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.ssvisualizer" label="State System Explorer" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/events_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.statesystem.TmfStateSystemExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OPXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" label="Colors" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/colors_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.colors.ColorsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OPnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" label="Filters" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/filters_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.filter.FilterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OP3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.tmfUml2SDSyncView" label="Sequence Diagram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/sequencediagram_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.uml2sd.SDView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OQHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" label="Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.statistics.TmfStatisticsViewImpl"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OQXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" label="Histogram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.histogram.HistogramView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OQnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.views.eventdensity" label="Event Density" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.eventdensity.EventDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OQ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.synchronization" label="Synchronization" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/synced.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.synchronization.TmfSynchronizationView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3ORHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3ORXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3ORnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OR3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OSHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OSXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OSnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OS3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OTHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OTXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OTnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OT3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OUHInEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OUXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OUnInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.annotations.XMLAnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OU3InEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.contentmodel.ContentModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_WZ3OVHInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.tools.trace.ui.TabTrace" label="Trace" iconURI="platform:/plugin/org.riscvstudio.ide.tools.trace/icons/trace/trace.png" tooltip="" category="RV Trace" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.riscvstudio.ide.tools.trace.ui.TabTrace"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.riscvstudio.ide.tools.trace"/>
    <tags>View</tags>
    <tags>categoryTag:RV Trace</tags>
  </descriptors>
  <trimContributions xmi:id="_WZ4bsXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_WZ4bsnInEfCAZJ6UDgwwcw" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_WZ4bs3InEfCAZJ6UDgwwcw" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_WZ4btHInEfCAZJ6UDgwwcw" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_WZ4bxHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4bxXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4bxnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4bx3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4byHInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_WZ5C_XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4byXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4bynInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin.selection" commandName="Zoom in (selection)" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4by3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4bzHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_WZ5DFnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4bzXInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_WZ5DC3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4bznInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4bz3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b0HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b0XInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b0nInEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4b03InEfCAZJ6UDgwwcw" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_WZ4b1HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b1XInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b1nInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b13InEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_WZ5DHHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b2HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b2XInEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b2nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_WZ5DFXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b23InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_WZ5DFXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b3HInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_WZ5C7nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b3XInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b3nInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b33InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.copy_to_clipboard" commandName="Copy to Clipboard" description="Copy to Clipboard" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b4HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugRemoteExecutable" commandName="Debug Remote Executable" description="Debug a Remote executable" category="_WZ5DFXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b4XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_WZ5DAXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b4nInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b43InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableLogger" commandName="Disable Logger" description="Disable Logger" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b5HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b5XInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.delete" commandName="Delete" description="Delete Target Node" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b5nInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDDown" commandName="Scroll down" description="Scroll down the sequence diagram" category="_WZ5DG3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b53InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.analysis.xml.ui.managexmlanalyses" commandName="Manage XML analyses..." description="Manage XML files containing analysis information" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b6HInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_WZ5DCXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b6XInEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_WZ5DEHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b6nInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_WZ5DC3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b63InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_WZ5C9nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b7HInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b7XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b7nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b73InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b8HInEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_WZ5C8XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b8XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b8nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b83InEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_WZ5DEHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b9HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b9XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b9nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b93InEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_WZ5DC3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b-HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b-XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b-nInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b-3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.open_as_experiment" commandName="Open As Experiment..." description="Open selected traces as an experiment" category="_WZ5C83InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4b_HInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4b_XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b_nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4b_3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cAHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.launch.clearMarkersCommand" commandName="Remove Valgrind Markers" description="Removes all Valgrind markers" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cAXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_WZ5DHnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cAnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.save" commandName="Save..." description="Save session(s)" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cA3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cBHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugAttachedExecutable" commandName="Debug Attached Executable" description="Debug an attached executable" category="_WZ5DFXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cBXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cBnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cB3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cCHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_WZ5DHXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cCXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cCnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.actions.KeyActionCommand" commandName="Insert ChangeLog entry" description="Insert a ChangeLog entry" category="_WZ5C73InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cC3InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cDHInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cDXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cDnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout.selection" commandName="Zoom out (selection)" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cD3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cEHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4cEXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4cEnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cE3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnChannel" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cFHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_WZ5C7nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cFXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_WZ5DD3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cFnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cF3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cGHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cGXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cGnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cG3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cHHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cHXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cHnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cH3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput" commandName="Show Build Output" category="_WZ5C63InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cIHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cIXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.prepareCommit" commandName="Prepare Commit" description="Copies latest changelog entry to clipboard" category="_WZ5C73InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cInInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cI3InEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.updateCommand" commandName="Refresh" category="_WZ5C93InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cJHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cJXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cJnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.executeScript" commandName="Execute Command Script..." description="Execute Command Script" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cJ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.remove" commandName="Remove" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cKHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnChannel" commandName="Enable Event..." description="Enable Event" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cKXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gdbtrace.ui.command.project.trace.selectexecutable" commandName="Select Trace Executable..." description="Select executable binary file for a GDB trace" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cKnInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_WZ5DC3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cK3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.start" commandName="Start" description="Start Trace Session" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cLHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cLXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.openFile" commandName="Open File" description="Opens a file" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cLnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cL3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4cMHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4cMXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cMnInEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cM3InEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_WZ5DBnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cNHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cNXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cNnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cN3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_WZ5DEnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cOHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cOXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cOnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cO3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cPHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cPXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cPnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cP3InEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_WZ5DGXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cQHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cQXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cQnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cQ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cRHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cRXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableChannel" commandName="Disable Channel" description="Disable a Trace Channel" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cRnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cR3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cSHInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_WZ5DAnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cSXInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_WZ5DC3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cSnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cS3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_WZ5C83InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4cTHInEfCAZJ6UDgwwcw" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_WZ4cTXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cTnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cT3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_WZ5DEnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cUHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cUXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cUnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cU3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cVHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cVXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cVnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cV3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cWHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cWXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cWnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cW3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElement" commandName="Open Build Element" category="_WZ5C63InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4cXHInEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_WZ4cXXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_WZ5C9nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cXnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.team.ui.commands.CopyCommitMessage" commandName="Copy Commit Message for Task" description="Copies a commit message for the currently selected task to the clipboard." category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cX3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cYHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cYXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cYnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cY3InEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cZHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cZXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cZnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cZ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4caHInEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4caXInEfCAZJ6UDgwwcw" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_WZ4canInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.filter" commandName="Filter Time Graph events" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ca3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cbHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cbXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_WZ5C7XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cbnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_WZ5DDXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cb3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ccHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ccXInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_WZ5DGHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ccnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cc3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cdHInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.donate" commandName="Sponsor" description="Sponsor to the development of the Eclipse IDE" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cdXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.logger" commandName="Enable Logger..." description="Assign Logger to Session and Enable Logger" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cdnInEfCAZJ6UDgwwcw" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cd3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ceHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ceXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.remote.ui.command.fetchlog" commandName="Fetch Remote Traces..." category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cenInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ce3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cfHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cfXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cfnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cf3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_WZ5DEnInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4cgHInEfCAZJ6UDgwwcw" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_WZ4cgXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cgnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cg3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4chHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4chXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_WZ5C7XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4chnInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_WZ5C_XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ch3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableEvent" commandName="Disable Event" description="Disable Event" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ciHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ciXInEfCAZJ6UDgwwcw" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cinInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ci3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cjHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cjXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cjnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cj3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4ckHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_WZ4ckXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cknInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ck3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4clHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_WZ5DCXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4clXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4clnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cl3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cmHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.left" commandName="Left" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cmXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cmnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cm3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromTest" commandName="New Task From Test" category="_WZ5C63InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cnHInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_WZ5DC3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cnXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cnnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_WZ5C9XInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4cn3InEfCAZJ6UDgwwcw" elementId="url" name="URL"/>
    <parameters xmi:id="_WZ4coHInEfCAZJ6UDgwwcw" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_WZ4coXInEfCAZJ6UDgwwcw" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_WZ4conInEfCAZJ6UDgwwcw" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_WZ4co3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cpHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cpXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cpnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cp3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cqHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_WZ5C9XInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4cqXInEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4cqnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cq3InEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_WZ5DEHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4crHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4crXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4crnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cr3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.exportToText" commandName="Export to Text..." description="Export trace to text..." category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4csHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4csXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4csnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cs3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ctHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_WZ5C7nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ctXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ctnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_add" commandName="Add External Analysis" description="Add External Analysis" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ct3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cuHInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cuXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnSession" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cunInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cu3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeEnd" commandName="Show node end" description="Show the node end" category="_WZ5DG3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cvHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cvXInEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_WZ5C8XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cvnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.importtracepkg" commandName="Import Trace Package..." category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cv3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cwHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cwXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cwnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cw3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cxHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disconnect" commandName="Disconnect" description="Disconnect to Target Node" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cxXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.managecustomparsers" commandName="Manage Custom Parsers..." description="Manage Custom Parsers" category="_WZ5DBHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cxnInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_SDK_Documentation" commandName="Nuclei SDK Documentation"/>
  <commands xmi:id="_WZ4cx3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cyHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cyXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cynInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cy3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4czHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnEvent" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4czXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout" commandName="Zoom out (mouse position)" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cznInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4cz3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c0HInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_WZ5DCXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c0XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c0nInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c03InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c1HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c1XInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c1nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_WZ5C83InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4c13InEfCAZJ6UDgwwcw" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_WZ4c2HInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.report_delete" commandName="Delete Report" description="Delete this report from the project" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c2XInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_WZ5DDHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c2nInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c23InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c3HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c3XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c3nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c33InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDUp" commandName="Scroll up" description="Scroll up the sequence diagram" category="_WZ5DG3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c4HInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_WZ5DGHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c4XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c4nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c43InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c5HInEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_WZ5DGXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c5XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c5nInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c53InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c6HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c6XInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_WZ5C_XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c6nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c63InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c7HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c7XInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c7nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c73InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnDomain" commandName="Enable Channel..." description="Enable a Trace Channel" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c8HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c8XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_WZ5DFXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c8nInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c83InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c9HInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog" commandName="Prepare Changelog" description="Prepares Changelog" category="_WZ5C73InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c9XInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c9nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c93InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.createGist" commandName="Create Gist" description="Create Gist based on selection" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4c-HInEfCAZJ6UDgwwcw" elementId="publicGist" name="Public Gist"/>
  </commands>
  <commands xmi:id="_WZ4c-XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c-nInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c-3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.rebasePullRequest" commandName="Rebase pull request" description="Rebase onto destination branch" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c_HInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c_XInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_WZ5DFnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c_nInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4c_3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dAHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_WZ5DDnInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dAXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_WZ4dAnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dA3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dBHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dBXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dBnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dB3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dCHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dCXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dCnInEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_WZ5DBnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dC3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.trim_trace" commandName="Export Time Selection as New Trace..." description="Create a new trace containing only the events in the currently selected time range. Only available if the trace type supports it, and if a time range is selected." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dDHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dDXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dDnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dD3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dEHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_WZ5DHnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dEXInEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_WZ5DEHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dEnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_WZ5C83InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dE3InEfCAZJ6UDgwwcw" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_WZ4dFHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dFXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dFnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dF3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dGHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dGXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.event" commandName="Enable Event..." description="Assign Event to Session and Channel and Enable Event" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dGnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dG3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dHHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dHXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dHnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.delete_suppl_files" commandName="Delete Supplementary Files..." category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dH3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dIHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dIXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dInInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_WZ5C9nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dI3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoom.selection" commandName="Zoom to selection" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dJHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dJXInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_WZ5DC3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dJnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_WZ5DEnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dJ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dKHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dKXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dKnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_WZ5DAHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dK3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dLHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dLXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_WZ5C9XInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dLnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_WZ4dL3InEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dMHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin" commandName="Zoom in (mouse position)" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dMXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_WZ5DEnInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dMnInEfCAZJ6UDgwwcw" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_WZ4dM3InEfCAZJ6UDgwwcw" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_WZ4dNHInEfCAZJ6UDgwwcw" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4dNXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.right" commandName="Right" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dNnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dN3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dOHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dOXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_WZ5C9nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dOnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dO3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_WZ5DFHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dPHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_WZ5DD3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dPXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dPnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dP3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dQHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4dQXInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_System_Technology_Homepage" commandName="Nuclei System Technology Homepage"/>
  <commands xmi:id="_WZ4dQnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dQ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.newConnection" commandName="New Connection..." description="New Connection to Target Node" category="_WZ5C-XInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dRHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.control.ui.remoteServicesIdParameter" name="Remote Services ID"/>
    <parameters xmi:id="_WZ4dRXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.control.ui.connectionNameParameter" name="Connection Name"/>
  </commands>
  <commands xmi:id="_WZ4dRnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_WZ5DDnInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dR3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4dSHInEfCAZJ6UDgwwcw" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dSXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.new_folder" commandName="New Folder..." description="Create a new trace folder" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dSnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dS3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_WZ5C_HInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dTHInEfCAZJ6UDgwwcw" elementId="title" name="Title"/>
    <parameters xmi:id="_WZ4dTXInEfCAZJ6UDgwwcw" elementId="message" name="Message"/>
    <parameters xmi:id="_WZ4dTnInEfCAZJ6UDgwwcw" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_WZ4dT3InEfCAZJ6UDgwwcw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_WZ4dUHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.load" commandName="Load..." description="Load session(s)" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dUXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dUnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_WZ5DH3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dU3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dVHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dVXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDLeft" commandName="Scroll left" description="Scroll left the sequence diagram" category="_WZ5DG3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dVnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dV3InEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_WZ5DEHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dWHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dWXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dWnInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_WZ5DGHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dW3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dXHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dXXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dXnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dX3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_WZ5C7nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dYHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dYXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_WZ5DFXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dYnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_WZ5DD3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dY3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_WZ5C9XInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dZHInEfCAZJ6UDgwwcw" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_WZ4dZXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dZnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dZ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4daHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_WZ5C6XInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4daXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4danInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4da3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dbHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dbXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dbnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4db3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dcHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dcXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dcnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dc3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ddHInEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ddXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_WZ5DFnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ddnInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.newProject" commandName="newProject" category="_WZ5DF3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dd3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4deHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4deXInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4denInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4de3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dfHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dfXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dfnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_WZ5DH3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4df3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.clear_offset" commandName="Clear Time Offset" description="Clear time offset" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dgHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dgXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dgnInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dg3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults" commandName="Show Test Results" category="_WZ5C63InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dhHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dhXInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dhnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dh3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.timegraph.bookmark" commandName="Toggle Bookmark..." category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4diHInEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4diXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dinInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4di3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4djHInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Documentation" commandName="NMSIS Documentation"/>
  <commands xmi:id="_WZ4djXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4djnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_WZ5C9nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dj3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_WZ5DGnInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dkHInEfCAZJ6UDgwwcw" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_WZ4dkXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dknInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dk3InEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dlHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugCore" commandName="Debug Core File" description="Debug a corefile" category="_WZ5DFXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dlXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dlnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.mergePullRequest" commandName="Merge pull request" description="Merge into destination branch" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dl3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dmHInEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_WZ5C9HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dmXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_WZ5C7XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dmnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dm3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.formatChangeLog" commandName="Format ChangeLog" description="Formats ChangeLog" category="_WZ5C73InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dnHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dnXInEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_WZ5DEnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dnnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dn3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_WZ5DBXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4doHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4doXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_WZ5C7nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4donInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4do3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dpHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_WZ5C7nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dpXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dpnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dp3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dqHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dqXInEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4dqnInEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_WZ4dq3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4drHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4drXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4drnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDRight" commandName="Scroll right" description="Scroll right the sequence diagram" category="_WZ5DG3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dr3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dsHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dsXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dsnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4ds3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_WZ5C7nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dtHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dtXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.analysis_help" commandName="Help" description="Help" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dtnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dt3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4duHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4duXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dunInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4du3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dvHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dvXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dvnInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Studio_User_Guide" commandName="Nuclei Studio User Guide"/>
  <commands xmi:id="_WZ4dv3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_WZ5DHnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dwHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_WZ5C-HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dwXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dwnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.refresh" commandName="Refresh" description="Refresh Node Configuration" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dw3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_WZ5DHHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dxHInEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_WZ5C8XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dxXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnSession" commandName="Enable Channel..." description="Enable a Trace Channel" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dxnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dx3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dyHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dyXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dynInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dy3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dzHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dzXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dznInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.import" commandName="Import..." description="Import traces into project" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4dz3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d0HInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d0XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d0nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d03InEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_WZ5DGHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d1HInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d1XInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.exporttracepkg" commandName="Export Trace Package..." category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d1nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d13InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_WZ5DHnInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4d2HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4d2XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d2nInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d23InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_WZ5DB3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4d3HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_WZ4d3XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_WZ4d3nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_WZ4d33InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_WZ5C9nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d4HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_WZ5C-HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d4XInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d4nInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ4d43InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_WZ5DDnInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ4d5HInEfCAZJ6UDgwwcw" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_WZ4d5XInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BQHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BQXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.createSession" commandName="Create Session..." description="Create a Trace Session" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BQnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BQ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BRHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.launch.exportCommand" commandName="Export Valgrind Log Files" description="Exports Valgrind log output to a directory" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BRXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BRnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_WZ5C8HInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5BR3InEfCAZJ6UDgwwcw" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_WZ5BSHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BSXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BSnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser" commandName="Open Build with Browser" category="_WZ5C63InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5BS3InEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_WZ5BTHInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BTXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_WZ5DEnInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5BTnInEfCAZJ6UDgwwcw" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_WZ5BT3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BUHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BUXInEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BUnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BU3InEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_WZ5DGXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BVHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BVXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BVnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BV3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.synchronize_traces" commandName="Synchronize Traces..." description="Synchronize 2 or more traces" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BWHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BWXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BWnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.checkoutPullRequest" commandName="Checkout Pull Request" description="Checkout pull request into topic branch" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BW3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BXHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_WZ5DFXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BXXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BXnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BX3InEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_WZ5DGHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BYHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableLogger" commandName="Enable Logger" description="Enable Logger" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BYXInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BYnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BY3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromBuild" commandName="New Task From Build" category="_WZ5C63InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BZHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_WZ5DD3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BZXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BZnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog2" commandName="Prepare Changelog In Editor" description="Prepares ChangeLog in an editor" category="_WZ5C73InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BZ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.select_traces" commandName="Select Traces..." description="Select Traces" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BaHInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_WZ5DC3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BaXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_WZ5DEnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BanInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Ba3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BbHInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE's most fiercely contested preferences" category="_WZ5DC3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BbXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BbnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bb3InEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_WZ5DEnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BcHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.destroySession" commandName="Destroy Session..." description="Destroy a Trace Session" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BcXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BcnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bc3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BdHInEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_WZ5DGXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BdXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BdnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bd3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.fetchPullRequest" commandName="Fetch Pull Request Commits" description="Fetch commits from pull request" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BeHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BeXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BenInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Be3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BfHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_WZ5DFnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BfXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BfnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bf3InEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convert.commands" commandName="RISC-V Project Convert Tool" category="_WZ5DEXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BgHInEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_WZ5DEHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BgXInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.generate.xml" commandName="XML File..." description="Generate a XML file from the selected DTD or Schema" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BgnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_WZ5DHXInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5Bg3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_WZ5BhHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BhXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BhnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bh3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BiHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnDomain" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BiXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BinInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_WZ5C9nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bi3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BjHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BjXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_WZ5C8nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BjnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bj3InEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BkHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput.url" commandName="Show Build Output" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BkXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BknInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_WZ5DHnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bk3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BlHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BlXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BlnInEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bl3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BmHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BmXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BmnInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bm3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.GoToMessage" commandName="Go to associated message" description="Go to the associated message" category="_WZ5DG3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BnHInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_WZ5DGHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BnXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BnnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bn3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BoHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BoXInEfCAZJ6UDgwwcw" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BonInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bo3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BpHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BpXInEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_WZ5DEnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BpnInEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_WZ5DEHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bp3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_WZ5C9nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BqHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_WZ5DBXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BqXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BqnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bq3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BrHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BrXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_WZ5DHHInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5BrnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_WZ5Br3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_WZ5BsHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BsXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BsnInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bs3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BtHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BtXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BtnInEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_WZ5DEHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bt3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BuHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Revision Information" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BuXInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Studio_FAQ" commandName="Nuclei Studio FAQ"/>
  <commands xmi:id="_WZ5BunInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_WZ5DE3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bu3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BvHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BvXInEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BvnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bv3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.cloneGist" commandName="Clone Gist" description="Clone Gist into Git repository" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BwHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnDomain" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BwXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BwnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_WZ5DHXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bw3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_WZ5C83InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BxHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BxXInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BxnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bx3InEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.import.package" commandName="Export to Package Management" category="_WZ5C-3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5ByHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5ByXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BynInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5By3InEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BzHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BzXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5BznInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Bz3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_WZ5DEnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B0HInEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.openLaunchSelector" commandName="Open Launch Bar Config Selector" category="_WZ5DGXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B0XInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults.url" commandName="Show Test Results" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B0nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B03InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B1HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B1XInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B1nInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B13InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B2HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B2XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_WZ5C_HInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5B2nInEfCAZJ6UDgwwcw" elementId="title" name="Title"/>
    <parameters xmi:id="_WZ5B23InEfCAZJ6UDgwwcw" elementId="message" name="Message"/>
    <parameters xmi:id="_WZ5B3HInEfCAZJ6UDgwwcw" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_WZ5B3XInEfCAZJ6UDgwwcw" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_WZ5B3nInEfCAZJ6UDgwwcw" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_WZ5B33InEfCAZJ6UDgwwcw" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_WZ5B4HInEfCAZJ6UDgwwcw" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_WZ5B4XInEfCAZJ6UDgwwcw" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_WZ5B4nInEfCAZJ6UDgwwcw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_WZ5B43InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B5HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B5XInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.showPerspectiveCommand" commandName="Switch to CMSIS Packs Perspective" category="_WZ5C93InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B5nInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B53InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B6HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B6XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B6nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_WZ5DBXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B63InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.connect" commandName="Connect" description="Connect to Target Node" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B7HInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B7XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B7nInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.new_experiment" commandName="New..." description="Create Tracing Experiment" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B73InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B8HInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B8XInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B8nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B83InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B9HInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B9XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5B9nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_WZ5B93InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_WZ5B-HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B-XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B-nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_WZ5DBXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B-3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B_HInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B_XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_WZ5DH3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B_nInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_WZ5DEnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5B_3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.stop" commandName="Stop" description="Stop Trace Session" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CAHInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sampleCommand" commandName="SDK Configuration Tools" category="_WZ5DF3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CAXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CAnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.select_trace_type" commandName="Select Trace Type..." description="Select a trace type" category="_WZ5C_3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5CA3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_WZ5CBHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CBXInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_WZ5DC3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CBnInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CB3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CCHInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CCXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CCnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.AbortBuild" commandName="Abort Build" category="_WZ5C63InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CC3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CDHInEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CDXInEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_WZ5DBnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CDnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CD3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CEHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CEXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_WZ5DFnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CEnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.convert_project" commandName="Configure or convert to Tracing Project" description="Configure or convert project to tracing project" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CE3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CFHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CFXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CFnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.import" commandName="Import..." description="Import Traces to LTTng Project" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CF3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CGHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CGXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_WZ5C7XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CGnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CG3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CHHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CHXInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_WZ5DD3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CHnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CH3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CIHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_WZ5DAXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CIXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CInInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CI3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_WZ5DEHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CJHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_WZ5C7nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CJXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CJnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.offset_traces" commandName="Apply Time Offset..." description="Shift traces by a constant time offset" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CJ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CKHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_WZ5C8HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CKXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CKnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CK3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CLHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CLXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_WZ5DBXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CLnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeStart" commandName="Show node start " description="Show the node start" category="_WZ5DG3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CL3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CMHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CMXInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CMnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CM3InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CNHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5CNXInEfCAZJ6UDgwwcw" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_WZ5CNnInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.snapshot" commandName="Record Snapshot" description="Record a snapshot" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CN3InEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5COHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_WZ5C9XInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5COXInEfCAZJ6UDgwwcw" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_WZ5COnInEfCAZJ6UDgwwcw" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_WZ5CO3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CPHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEvent" commandName="Enable Event" description="Enable Event" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CPXInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sdkManageCommand" commandName="NPK Package Management" category="_WZ5C_nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CPnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_WZ5DEnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CP3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CQHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_WZ5DAXInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CQXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_WZ5DDnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CQnInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CQ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CRHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CRXInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CRnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_WZ5C83InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5CR3InEfCAZJ6UDgwwcw" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_WZ5CSHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CSXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.CopyDetails" commandName="Copy Details" category="_WZ5C63InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5CSnInEfCAZJ6UDgwwcw" elementId="kind" name="Kind"/>
    <parameters xmi:id="_WZ5CS3InEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_WZ5CTHInEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CTXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.RunBuild" commandName="Run Build" category="_WZ5C63InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CTnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CT3InEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CUHInEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CUXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CUnInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_WZ5C7nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CU3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CVHInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_WZ5DGnInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CVXInEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CVnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CV3InEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_WZ5DA3InEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5CWHInEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_WZ5CWXInEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_WZ5CWnInEfCAZJ6UDgwwcw" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_WZ5DEnInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5CW3InEfCAZJ6UDgwwcw" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_WZ5CXHInEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_remove" commandName="Remove External Analysis" description="Remove External Analysis" category="_WZ5C_3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CXXInEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_WZ5DEHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CXnInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CX3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CYHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CYXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser.url" commandName="Open Build with Browser" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CYnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CY3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CZHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CZXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_WZ5C-nInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CZnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_WZ5C7HInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CZ3InEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_WZ5C6XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CaHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_WZ5C9XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CaXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CanInEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Ca3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_WZ5DDnInEfCAZJ6UDgwwcw">
    <parameters xmi:id="_WZ5CbHInEfCAZJ6UDgwwcw" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_WZ5CbXInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannel" commandName="Enable Channel" description="Enable a Trace Channel" category="_WZ5C-XInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CbnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_WZ5DCHInEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cb3InEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convertall.commands" commandName="org.riscvstudio.ide.project.convertall.commands"/>
  <commands xmi:id="_WZ5CcHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CcXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CcnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cc3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CdHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CdXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CdnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cd3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CeHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CeXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CenInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Ce3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CfHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CfXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CfnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cf3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CgHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CgXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CgnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cg3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5ChHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5ChXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5ChnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Ch3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CiHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CiXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CinInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.doc.actionSet/org.eclipse.mylyn.tasks.ui.bug.report" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Ci3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CjHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CjXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CjnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cj3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CkHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CkXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CknInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Ck3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5ClHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5ClXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5ClnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cl3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CmHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CmXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CmnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cm3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CnHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CnXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CnnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cn3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CoHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CoXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5ConInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Co3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CpHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CpXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CpnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cp3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CqHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CqXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CqnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cq3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CrHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CrXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CrnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cr3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CsHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CsXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CsnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cs3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CtHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CtXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CtnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Ct3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CuHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CuXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.cdt.ui.cview.contribution/org.eclipse.mylyn.cdt.ui.cview.focusActiveTask.action" commandName="Focus on Active Task" description="Focus only on elements in active Mylyn task" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CunInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cu3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CvHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CvXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CvnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cv3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CwHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CwXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CwnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cw3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CxHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CxXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CxnInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cx3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CyHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CyXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CynInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cy3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CzHInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CzXInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5CznInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5Cz3InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C0HInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.search.contribution/org.eclipse.mylyn.ide.ui.actions.focus.search.results" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C0XInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C0nInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C03InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C1HInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C1XInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C1nInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C13InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C2HInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C2XInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C2nInEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_WZ5C23InEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_WZ5DA3InEfCAZJ6UDgwwcw"/>
  <addons xmi:id="_WZ5C3HInEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_WZ5C3XInEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_WZ5C3nInEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_WZ5C33InEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_WZ5C4HInEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_WZ5C4XInEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_WZ5C4nInEfCAZJ6UDgwwcw" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_WZ5C43InEfCAZJ6UDgwwcw" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_WZ5C5HInEfCAZJ6UDgwwcw" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_WZ5C5XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_WZ5C5nInEfCAZJ6UDgwwcw" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_WZ5C53InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_WZ5C6HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_WZ5C6XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_WZ5C6nInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_WZ5C63InEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.category.Commands" name="Builds"/>
  <categories xmi:id="_WZ5C7HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_WZ5C7XInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_WZ5C7nInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_WZ5C73InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog" name="Changelog" description="Changelog key bindings"/>
  <categories xmi:id="_WZ5C8HInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_WZ5C8XInEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_WZ5C8nInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.commands" name="CDT Context" description="CDT Task-Focused Interface Commands"/>
  <categories xmi:id="_WZ5C83InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_WZ5C9HInEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_WZ5C9XInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_WZ5C9nInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_WZ5C93InEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.category" name="C/C++ Packages Category"/>
  <categories xmi:id="_WZ5C-HInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_WZ5C-XInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.category" name="LTTng Trace Control Commands" description="LTTng Trace Control Commands"/>
  <categories xmi:id="_WZ5C-nInEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_WZ5C-3InEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.import.package" name="Export to Package Management"/>
  <categories xmi:id="_WZ5C_HInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_WZ5C_XInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_WZ5C_nInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sdkManage" name="NPK Package Management"/>
  <categories xmi:id="_WZ5C_3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commands.category" name="Tracing" description="Tracing Commands"/>
  <categories xmi:id="_WZ5DAHInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_WZ5DAXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_WZ5DAnInEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_WZ5DA3InEfCAZJ6UDgwwcw" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_WZ5DBHInEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commands.parser.category" name="Parser Commands" description="Parser Commands"/>
  <categories xmi:id="_WZ5DBXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
  <categories xmi:id="_WZ5DBnInEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_WZ5DB3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_WZ5DCHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_WZ5DCXInEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_WZ5DCnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_WZ5DC3InEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_WZ5DDHInEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.category" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_WZ5DDXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_WZ5DDnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_WZ5DD3InEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_WZ5DEHInEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_WZ5DEXInEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convert.commands.category" name="RISC-V Project Convert Tool"/>
  <categories xmi:id="_WZ5DEnInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_WZ5DE3InEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_WZ5DFHInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_WZ5DFXInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_WZ5DFnInEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_WZ5DF3InEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.category" name="SDK Configuration Tools"/>
  <categories xmi:id="_WZ5DGHInEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_WZ5DGXInEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_WZ5DGnInEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_WZ5DG3InEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.category" name="UML2 Sequence Diagram Viewer Commands" description="UML2 Sequence Diagram Viewer Commands"/>
  <categories xmi:id="_WZ5DHHInEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_WZ5DHXInEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_WZ5DHnInEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_WZ5DH3InEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
</application:Application>
