/*
 * 音频数据头文件 - pdm_audio_02
 */

#ifndef PDM_AUDIO_02_DATA_H
#define PDM_AUDIO_02_DATA_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 音频参数
#define AUDIO_SAMPLE_RATE    8000
#define AUDIO_SAMPLES        24000
#define AUDIO_DURATION_MS    3000

// 数据声明
extern const int16_t pdm_audio_02_int16[AUDIO_SAMPLES];
extern const float pdm_audio_02_float32[AUDIO_SAMPLES];

#ifdef __cplusplus
}
#endif

#endif // PDM_AUDIO_02_DATA_H
