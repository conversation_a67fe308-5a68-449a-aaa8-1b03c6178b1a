/*
 * NMSIS MFCC系数文件
 * 包含汉明窗、<PERSON>滤波器参数、DCT系数等
 * 生成时间: 2025-07-18 19:51:53
 */

#include <stdint.h>
#include "riscv_math.h"

// ===== MFCC参数定义 =====
#define MFCC_SAMPLE_RATE     8000
#define MFCC_N_FFT           256
#define MFCC_WIN_LENGTH      200
#define MFCC_HOP_LENGTH      80
#define MFCC_N_MELS          26
#define MFCC_N_MFCC          13

// ===== 汉明窗系数 =====
const float32_t mfcc_hamming_window[MFCC_WIN_LENGTH] = {
    0.080000f, 0.080227f, 0.080908f, 0.082041f, 0.083627f, 0.085663f, 0.088148f, 0.091078f,
    0.094452f, 0.098265f, 0.102514f, 0.107195f, 0.112303f, 0.117833f, 0.123780f, 0.130137f,
    0.136899f, 0.144059f, 0.151609f, 0.159543f, 0.167852f, 0.176529f, 0.185564f, 0.194949f,
    0.204674f, 0.214731f, 0.225108f, 0.235797f, 0.246785f, 0.258063f, 0.269619f, 0.281442f,
    0.293520f, 0.305841f, 0.318393f, 0.331164f, 0.344142f, 0.357312f, 0.370663f, 0.384181f,
    0.397852f, 0.411664f, 0.425603f, 0.439654f, 0.453805f, 0.468040f, 0.482347f, 0.496710f,
    0.511116f, 0.525551f, 0.540000f, 0.554449f, 0.568884f, 0.583290f, 0.597653f, 0.611960f,
    0.626195f, 0.640346f, 0.654397f, 0.668336f, 0.682148f, 0.695819f, 0.709337f, 0.722688f,
    0.735859f, 0.748836f, 0.761607f, 0.774159f, 0.786480f, 0.798558f, 0.810381f, 0.821937f,
    0.833215f, 0.844203f, 0.854892f, 0.865269f, 0.875326f, 0.885051f, 0.894436f, 0.903471f,
    0.912148f, 0.920457f, 0.928391f, 0.935941f, 0.943101f, 0.949863f, 0.956220f, 0.962167f,
    0.967697f, 0.972805f, 0.977486f, 0.981735f, 0.985548f, 0.988922f, 0.991852f, 0.994337f,
    0.996373f, 0.997958f, 0.999092f, 0.999773f, 1.000000f, 0.999773f, 0.999092f, 0.997958f,
    0.996373f, 0.994337f, 0.991852f, 0.988922f, 0.985548f, 0.981735f, 0.977486f, 0.972805f,
    0.967697f, 0.962167f, 0.956220f, 0.949863f, 0.943101f, 0.935941f, 0.928391f, 0.920457f,
    0.912148f, 0.903471f, 0.894436f, 0.885051f, 0.875326f, 0.865269f, 0.854892f, 0.844203f,
    0.833215f, 0.821937f, 0.810381f, 0.798558f, 0.786480f, 0.774159f, 0.761607f, 0.748836f,
    0.735859f, 0.722688f, 0.709337f, 0.695819f, 0.682148f, 0.668336f, 0.654397f, 0.640346f,
    0.626195f, 0.611960f, 0.597653f, 0.583290f, 0.568884f, 0.554449f, 0.540000f, 0.525551f,
    0.511116f, 0.496710f, 0.482347f, 0.468040f, 0.453805f, 0.439654f, 0.425603f, 0.411664f,
    0.397852f, 0.384181f, 0.370663f, 0.357312f, 0.344142f, 0.331164f, 0.318393f, 0.305841f,
    0.293520f, 0.281442f, 0.269619f, 0.258063f, 0.246785f, 0.235797f, 0.225108f, 0.214731f,
    0.204674f, 0.194949f, 0.185564f, 0.176529f, 0.167852f, 0.159543f, 0.151609f, 0.144059f,
    0.136899f, 0.130137f, 0.123780f, 0.117833f, 0.112303f, 0.107195f, 0.102514f, 0.098265f,
    0.094452f, 0.091078f, 0.088148f, 0.085663f, 0.083627f, 0.082041f, 0.080908f, 0.080227f
};

// ===== Mel滤波器起始位置 =====
const uint32_t mfcc_filter_pos[MFCC_N_MELS] = {
     0,  1,  3,  5,  7,  9, 11, 14,
    17, 19, 23, 26, 29, 33, 37, 42,
    47, 52, 57, 63, 69, 76, 83, 91,
    99, 108
};

// ===== Mel滤波器长度 =====
const uint32_t mfcc_filter_len[MFCC_N_MELS] = {
    4, 5, 5, 5, 5, 6, 7, 6,
    7, 8, 7, 8, 9, 10, 11, 11,
    11, 12, 13, 14, 15, 16, 17, 18,
    20, 21
};

// ===== Mel滤波器系数 =====
const float32_t mfcc_filter_coeffs[271] = {
    0.000000f, 1.000000f, 0.500000f, 0.000000f, 0.000000f, 0.500000f, 1.000000f, 0.500000f,
    0.000000f, 0.000000f, 0.500000f, 1.000000f, 0.500000f, 0.000000f, 0.000000f, 0.500000f,
    1.000000f, 0.500000f, 0.000000f, 0.000000f, 0.500000f, 1.000000f, 0.500000f, 0.000000f,
    0.000000f, 0.500000f, 1.000000f, 0.666667f, 0.333333f, 0.000000f, 0.000000f, 0.333333f,
    0.666667f, 1.000000f, 0.666667f, 0.333333f, 0.000000f, 0.000000f, 0.333333f, 0.666667f,
    1.000000f, 0.500000f, 0.000000f, 0.000000f, 0.500000f, 1.000000f, 0.750000f, 0.500000f,
    0.250000f, 0.000000f, 0.000000f, 0.250000f, 0.500000f, 0.750000f, 1.000000f, 0.666667f,
    0.333333f, 0.000000f, 0.000000f, 0.333333f, 0.666667f, 1.000000f, 0.666667f, 0.333333f,
    0.000000f, 0.000000f, 0.333333f, 0.666667f, 1.000000f, 0.750000f, 0.500000f, 0.250000f,
    0.000000f, 0.000000f, 0.250000f, 0.500000f, 0.750000f, 1.000000f, 0.750000f, 0.500000f,
    0.250000f, 0.000000f, 0.000000f, 0.250000f, 0.500000f, 0.750000f, 1.000000f, 0.800000f,
    0.600000f, 0.400000f, 0.200000f, 0.000000f, 0.000000f, 0.200000f, 0.400000f, 0.600000f,
    0.800000f, 1.000000f, 0.800000f, 0.600000f, 0.400000f, 0.200000f, 0.000000f, 0.000000f,
    0.200000f, 0.400000f, 0.600000f, 0.800000f, 1.000000f, 0.800000f, 0.600000f, 0.400000f,
    0.200000f, 0.000000f, 0.000000f, 0.200000f, 0.400000f, 0.600000f, 0.800000f, 1.000000f,
    0.800000f, 0.600000f, 0.400000f, 0.200000f, 0.000000f, 0.000000f, 0.200000f, 0.400000f,
    0.600000f, 0.800000f, 1.000000f, 0.833333f, 0.666667f, 0.500000f, 0.333333f, 0.166667f,
    0.000000f, 0.000000f, 0.166667f, 0.333333f, 0.500000f, 0.666667f, 0.833333f, 1.000000f,
    0.833333f, 0.666667f, 0.500000f, 0.333333f, 0.166667f, 0.000000f, 0.000000f, 0.166667f,
    0.333333f, 0.500000f, 0.666667f, 0.833333f, 1.000000f, 0.857143f, 0.714286f, 0.571429f,
    0.428571f, 0.285714f, 0.142857f, 0.000000f, 0.000000f, 0.142857f, 0.285714f, 0.428571f,
    0.571429f, 0.714286f, 0.857143f, 1.000000f, 0.857143f, 0.714286f, 0.571429f, 0.428571f,
    0.285714f, 0.142857f, 0.000000f, 0.000000f, 0.142857f, 0.285714f, 0.428571f, 0.571429f,
    0.714286f, 0.857143f, 1.000000f, 0.875000f, 0.750000f, 0.625000f, 0.500000f, 0.375000f,
    0.250000f, 0.125000f, 0.000000f, 0.000000f, 0.125000f, 0.250000f, 0.375000f, 0.500000f,
    0.625000f, 0.750000f, 0.875000f, 1.000000f, 0.875000f, 0.750000f, 0.625000f, 0.500000f,
    0.375000f, 0.250000f, 0.125000f, 0.000000f, 0.000000f, 0.125000f, 0.250000f, 0.375000f,
    0.500000f, 0.625000f, 0.750000f, 0.875000f, 1.000000f, 0.888889f, 0.777778f, 0.666667f,
    0.555556f, 0.444444f, 0.333333f, 0.222222f, 0.111111f, 0.000000f, 0.000000f, 0.111111f,
    0.222222f, 0.333333f, 0.444444f, 0.555556f, 0.666667f, 0.777778f, 0.888889f, 1.000000f,
    0.900000f, 0.800000f, 0.700000f, 0.600000f, 0.500000f, 0.400000f, 0.300000f, 0.200000f,
    0.100000f, 0.000000f, 0.000000f, 0.100000f, 0.200000f, 0.300000f, 0.400000f, 0.500000f,
    0.600000f, 0.700000f, 0.800000f, 0.900000f, 1.000000f, 0.900000f, 0.800000f, 0.700000f,
    0.600000f, 0.500000f, 0.400000f, 0.300000f, 0.200000f, 0.100000f, 0.000000f
};

// ===== DCT系数矩阵 (13 x 26) =====
const float32_t mfcc_dct_matrix[MFCC_N_MFCC * MFCC_N_MELS] = {
    // Row 0
    1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f,
    1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f,
    1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f, 1.000000f,
    1.000000f, 1.000000f,
    // Row 1
    0.998176f, 0.983620f, 0.954721f, 0.911900f, 0.855781f, 0.787183f, 0.707107f, 0.616719f,
    0.517338f, 0.410413f, 0.297503f, 0.180255f, 0.060378f, -0.060378f, -0.180255f, -0.297503f,
    -0.410413f, -0.517338f, -0.616719f, -0.707107f, -0.787183f, -0.855781f, -0.911900f, -0.954721f,
    -0.983620f, -0.998176f,
    // Row 2
    0.992709f, 0.935016f, 0.822984f, 0.663123f, 0.464723f, 0.239316f, -0.000000f, -0.239316f,
    -0.464723f, -0.663123f, -0.822984f, -0.935016f, -0.992709f, -0.992709f, -0.935016f, -0.822984f,
    -0.663123f, -0.464723f, -0.239316f, -0.000000f, 0.239316f, 0.464723f, 0.663123f, 0.822984f,
    0.935016f, 0.992709f,
    // Row 3
    0.983620f, 0.855781f, 0.616719f, 0.297503f, -0.060378f, -0.410413f, -0.707107f, -0.911900f,
    -0.998176f, -0.954721f, -0.787183f, -0.517338f, -0.180255f, 0.180255f, 0.517338f, 0.787183f,
    0.954721f, 0.998176f, 0.911900f, 0.707107f, 0.410413f, 0.060378f, -0.297503f, -0.616719f,
    -0.855781f, -0.983620f,
    // Row 4
    0.970942f, 0.748511f, 0.354605f, -0.120537f, -0.568065f, -0.885456f, -1.000000f, -0.885456f,
    -0.568065f, -0.120537f, 0.354605f, 0.748511f, 0.970942f, 0.970942f, 0.748511f, 0.354605f,
    -0.120537f, -0.568065f, -0.885456f, -1.000000f, -0.885456f, -0.568065f, -0.120537f, 0.354605f,
    0.748511f, 0.970942f,
    // Row 5
    0.954721f, 0.616719f, 0.060378f, -0.517338f, -0.911900f, -0.983620f, -0.707107f, -0.180255f,
    0.410413f, 0.855781f, 0.998176f, 0.787183f, 0.297503f, -0.297503f, -0.787183f, -0.998176f,
    -0.855781f, -0.410413f, 0.180255f, 0.707107f, 0.983620f, 0.911900f, 0.517338f, -0.060378f,
    -0.616719f, -0.954721f,
    // Row 6
    0.935016f, 0.464723f, -0.239316f, -0.822984f, -0.992709f, -0.663123f, -0.000000f, 0.663123f,
    0.992709f, 0.822984f, 0.239316f, -0.464723f, -0.935016f, -0.935016f, -0.464723f, 0.239316f,
    0.822984f, 0.992709f, 0.663123f, 0.000000f, -0.663123f, -0.992709f, -0.822984f, -0.239316f,
    0.464723f, 0.935016f,
    // Row 7
    0.911900f, 0.297503f, -0.517338f, -0.983620f, -0.787183f, -0.060378f, 0.707107f, 0.998176f,
    0.616719f, -0.180255f, -0.855781f, -0.954721f, -0.410413f, 0.410413f, 0.954721f, 0.855781f,
    0.180255f, -0.616719f, -0.998176f, -0.707107f, 0.060378f, 0.787183f, 0.983620f, 0.517338f,
    -0.297503f, -0.911900f,
    // Row 8
    0.885456f, 0.120537f, -0.748511f, -0.970942f, -0.354605f, 0.568065f, 1.000000f, 0.568065f,
    -0.354605f, -0.970942f, -0.748511f, 0.120537f, 0.885456f, 0.885456f, 0.120537f, -0.748511f,
    -0.970942f, -0.354605f, 0.568065f, 1.000000f, 0.568065f, -0.354605f, -0.970942f, -0.748511f,
    0.120537f, 0.885456f,
    // Row 9
    0.855781f, -0.060378f, -0.911900f, -0.787183f, 0.180255f, 0.954721f, 0.707107f, -0.297503f,
    -0.983620f, -0.616719f, 0.410413f, 0.998176f, 0.517338f, -0.517338f, -0.998176f, -0.410413f,
    0.616719f, 0.983620f, 0.297503f, -0.707107f, -0.954721f, -0.180255f, 0.787183f, 0.911900f,
    0.060378f, -0.855781f,
    // Row 10
    0.822984f, -0.239316f, -0.992709f, -0.464723f, 0.663123f, 0.935016f, 0.000000f, -0.935016f,
    -0.663123f, 0.464723f, 0.992709f, 0.239316f, -0.822984f, -0.822984f, 0.239316f, 0.992709f,
    0.464723f, -0.663123f, -0.935016f, -0.000000f, 0.935016f, 0.663123f, -0.464723f, -0.992709f,
    -0.239316f, 0.822984f,
    // Row 11
    0.787183f, -0.410413f, -0.983620f, -0.060378f, 0.954721f, 0.517338f, -0.707107f, -0.855781f,
    0.297503f, 0.998176f, 0.180255f, -0.911900f, -0.616719f, 0.616719f, 0.911900f, -0.180255f,
    -0.998176f, -0.297503f, 0.855781f, 0.707107f, -0.517338f, -0.954721f, 0.060378f, 0.983620f,
    0.410413f, -0.787183f,
    // Row 12
    0.748511f, -0.568065f, -0.885456f, 0.354605f, 0.970942f, -0.120537f, -1.000000f, -0.120537f,
    0.970942f, 0.354605f, -0.885456f, -0.568065f, 0.748511f, 0.748511f, -0.568065f, -0.885456f,
    0.354605f, 0.970942f, -0.120537f, -1.000000f, -0.120537f, 0.970942f, 0.354605f, -0.885456f,
    -0.568065f, 0.748511f
};
